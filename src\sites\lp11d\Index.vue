<script setup>
import { ref, onMounted, provide, watch } from "vue";
import Axios from "axios";
import PageOne from "../lp11d/components/PageOne.vue";
import PageTwo from "../lp11d/components/PageTwo.vue";
import PageThree from "../lp11d/components/PageThree.vue";
import PageFour from "../lp11d/components/PageFour.vue";
import PageFive from "../lp11d/components/PageFive.vue";
import PageSix from "../lp11d/components/PageSix.vue";
import PageSeven from "../lp11d/components/PageSeven.vue";
import PageEight from "../lp11d/components/PageEight.vue";
import Navbar from "../../components/Navbar.vue";
import Footer from "../../components/Footer9.vue";
import Background from "../../components/Background.vue";
import Endpoints from "./../../assets/js/config/endpoints.json";
import Params from "./../../assets/js/helper/urlParameters.js";
import Media from "./../../assets/js/helper/deviceDetection.js";
import Language from "./../../assets/js/helper/Language.js";
import inputData from "./../../assets/js/config/forms.json";
import config from "../../assets/js/config/config.json";

const location = ref();
const device = ref(false);
const steps = ref(1);
const hasEmailParam = ref(false);

provide("assets", config.assets);

onMounted(async () => {
  const urlParams = new URLSearchParams(window.location.search);
  const emailParam = urlParams.get("email");

  hasEmailParam.value = !!emailParam;
  if (hasEmailParam.value) {
    inputData.email = {
      value: emailParam,
      valid: true,
      error: false,
      required: true,
    };
  }

  await Axios.get(Endpoints.geo).then((s) => {
    device.value = s.data;
    inputData.location.value = s.data.geo.city ? s.data.geo.city : "Nevada";
    inputData.device.value = s.data;
    inputData.lander.value = Params().path;
    inputData.click_id.value = Params().click_id;
    inputData.source_id.value = Params().source_id;
    inputData.country.value = s.data.geo.country;
    inputData.country_code.value = s.data.geo.country_code.toLowerCase();
    inputData.locale.value = Params().locale;
    inputData.media.value = Media().device;
    location.value = inputData.location.value;
    inputData.http.value = Params().http;
    inputData.t1.value = Params().t1;
    inputData.t2.value = Params().t2;
    inputData.l.value = Params().l;
    inputData.s3.value = Params().s3;
    inputData.cmp.value = Params().cmp;
    inputData.image.value = Params().image;
    inputData.traf_id.value = Params().traf_id;
inputData.tsid.value = Params().tsid;
inputData.alreadyRegistered.value = Params().alreadyRegistered;
    if (s.data.geo.postal_code) {
      inputData.postal_code.value = s.data.geo.postal_code;
    }
  });
  window.history.pushState({ page: 1 }, null);
  window.addEventListener("popstate", handlePopstate);

  function handlePopstate() {
    window.location.href = config.links.back_button + Params().url;
  }
});

const moveNextSlide = () => {
  if (hasEmailParam.value && steps.value === 4) {
    steps.value = 6;
  } else {
    steps.value++;
  }
};

const moveBackSlide = () => {
  if (hasEmailParam.value && steps.value === 6) {
    steps.value = 4;
  } else {
    steps.value--;
  }
};

watch(steps, (newValue) => {
  console.log("Step changed to:", newValue);
});
</script>

<template>
  <div class="layoutlp9">
    <Background />
    <Navbar />
    <div class="content-container d-flex justify-content-center" style="height: 100vh">
      <div class="bg-transparent">
        <h6 class="text-center" style="margin-top: 7em">
          <a class="text-warning text-decoration-none" href="https://go.blcdog.com/smartpop/40ed1cc1cf7b3714debca75a15f868161c8a9b68c6450191968ef0ed7836daf4?userId=39b80d1f53f8ed833ed76bfeac5b2b16bebe56489fe5bf8f2e4e923766b2e58f&memberId=&p1=page&sourceId=youcontrolher.com_tab">
            <img src="../../assets/fire.gif" alt="gif" style="width: 30px" />
            <span class="text-dark" style="text-decoration: underline; display: inline-block; width: fit-content">Control her on cam!!</span>
            <img src="../../assets/fire.gif" alt="gif" style="width: 30px" />
          </a>
        </h6>
        <div v-if="steps === 1">
          <PageOne :inputs="inputData" :steps="steps" :moveNextSlide="moveNextSlide" :moveBackSlide="moveBackSlide" :location="location" />
        </div>
        <div v-else-if="steps === 2">
          <PageTwo :inputs="inputData" :steps="steps" :moveNextSlide="moveNextSlide" :moveBackSlide="moveBackSlide" :location="location" />
        </div>
        <div v-else-if="steps === 3">
          <PageThree :inputs="inputData" :steps="steps" :moveNextSlide="moveNextSlide" :moveBackSlide="moveBackSlide" :location="location" />
        </div>
        <!-- <div v-else-if="steps === 4">
          <PageFour :inputs="inputData" :steps="steps" :moveNextSlide="moveNextSlide" :moveBackSlide="moveBackSlide" />
        </div> -->
        <div v-else-if="steps === 4">
          <PageFive :inputs="inputData" :steps="steps" :moveNextSlide="moveNextSlide" :moveBackSlide="moveBackSlide" :location="location" />
        </div>
        <div v-else-if="steps === 5 && !hasEmailParam">
          <PageSix :inputs="inputData" :steps="steps" :moveNextSlide="moveNextSlide" :moveBackSlide="moveBackSlide" :location="location" />
        </div>
        <div v-else-if="steps === 6 || (steps === 6 && hasEmailParam)">
          <PageSeven :inputs="inputData" :steps="steps" :moveNextSlide="moveNextSlide" :moveBackSlide="moveBackSlide" :location="location" />
        </div>
        <div v-else-if="steps === 7">
          <PageEight :inputs="inputData" :steps="steps" :moveNextSlide="moveNextSlide" :moveBackSlide="moveBackSlide" :location="location" />
        </div>
      </div>
    </div>
    <Footer :language="Language" class="mt-2" />
  </div>
</template>

<style>
.layoutlp9 {
  position: relative;
  overflow: hidden;
}

.content-container {
  position: relative;
  z-index: 1;
}

@media only screen and (max-width: 867px) {
  .lp8x {
    height: auto !important;
    margin-bottom: 20px !important;
  }
}
</style>
