<script setup>
import { ref, defineProps } from "vue";
import Steps from "../../lp9/components/views/StepsMarker.vue";
import Params from "../../../assets/js/helper/urlParameters.js";
import Language from "../../../assets/js/helper/Language.js";
import Searching from "../../../components/Searching.vue";

const props = defineProps(["steps", "moveNextSlide", "moveBackSlide", "language", "location", "inputs"]);
const steps = ref(props.steps);
const selectedAge = ref(null);

const validationMessages = ref({ dob: null });

const dob = (x, y) => {
  let min = parseInt(x);
  let max = parseInt(y);
  let random = Math.floor(Math.random() * (parseInt(max) - parseInt(min))) + min;
  props.inputs.birth_year.value = new Date().getFullYear() - (random + 1);
  props.inputs.birth_month.value = (Math.floor(Math.random() * (parseInt(12) - parseInt(1))) + 1).toString();
  props.inputs.birth_day.value = (Math.floor(Math.random() * (parseInt(29) - parseInt(1))) + 1).toString();

  props.inputs.birth_day.value.length == 1 ? (props.inputs.birth_day.value = "0" + props.inputs.birth_day.value) : "";
  props.inputs.birth_month.value.length == 1 ? (props.inputs.birth_month.value = "0" + props.inputs.birth_month.value) : "";

  props.moveNextSlide();
};
</script>

<template>
  <div class="container-fluid d-flex align-items-center justify-content-center min-vh-100">
    <div class="text-center">
      <p class="fs-3 mb-5 head">{{ Language.access_website }}</p>
      <p class="fs-1 mb-5 head">{{ Language.age_question }}</p>
      <div class="d-flex justify-content-center gap-3 mb-5 button-container">
        <button type="button" class="btn btn-outline-light rounded-5 fs-5 18-btn" style="height: 3em !important; width: 8em !important" @click="dob(18, 34)">18+</button>
        <button type="button" class="btn btn-outline-light rounded-5 fs-5 35-btn" style="height: 3em !important; width: 8em !important" @click="dob(35, 40)">35+</button>
        <button type="button" class="btn btn-outline-light rounded-5 fs-5 40-btn" style="height: 3em !important; width: 8em !important" @click="dob(40, 65)">40+</button>
        <button type="button" class="btn btn-outline-light rounded-5 fs-5 65-btn" style="height: 3em !important; width: 8em !important" @click="dob(65, 75)">65+</button>
      </div>
      <div class="steps d-flex gap-3 justify-content-center mb-3">
        <i class="fa-solid fa-star fs-3 text-white"></i>
        <i class="fa-solid fa-star fs-3 text-white"></i>
        <i class="fa-solid fa-star fs-3 text-white"></i>
        <i class="fa-solid fa-star fs-3 text-white"></i>
        <i class="fa-solid fa-star fs-3 text-white"></i>
        <i class="fa-regular fa-star fs-3 text-white"></i>
        <i class="fa-regular fa-star fs-3 text-white"></i>
      </div>
      <p v-if="validationMessages.dob" class="text-light fs-6 mt-2">({{ validationMessages.dob }})</p>
      <Searching :location="location" :language="Language" />
    </div>
    <Steps :step="steps" />
  </div>
</template>

<style scoped>
@media (max-width: 768px) {
  .fs-1 {
    font-size: 1.5rem;
  }
  .button-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
  .btn {
    width: 100%;
    height: 4em;
  }
  .steps {
    margin-top: 1em;
  }
  .head {
    margin-bottom: 3px !important;
  }
}
</style>
