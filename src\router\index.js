import { createRouter, createWebHistory } from "vue-router";
const site = "Sexy-Dates";

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: "/",
      name: "home",
      component: () => import("../sites/Home.vue"),
    },
    {
      path: "/privacy",
      name: "privacy",
      component: () => import("../sites/Privacy.vue"),
    },
    {
      path: "/terms",
      name: "terms",
      component: () => import("../sites/Terms.vue"),
    },
    {
      path: "/ccpa",
      name: "ccpa",
      component: () => import("../sites/CCPA.vue"),
    },
    {
      path: "/legal",
      name: "legal",
      component: () => import("../sites/Legal.vue"),
    },
    {
      path: "/lp2",
      name: "New Mobile Test Landing V.2",
      component: () => import("../sites/lp2/Index.vue"),
    },
    {
      path: "/lp4",
      name: "New Mobile Test Landing V.4",
      component: () => import("../sites/lp4/Index.vue"),
    },
    {
      path: "/lp4c",
      name: "New Mobile Test Landing V.1",
      component: () => import("../sites/lp4c/Index.vue"),
    },
    {
      path: "/lp2d",
      name: "New Mobile Test Landing V.5",
      component: () => import("../sites/lp2b/Index.vue"),
    },
    {
      path: "/lp2c",
      name: "New Mobile Test Landing V.6",
      component: () => import("../sites/lp2c/Index.vue"),
    },
    {
      path: "/lp2b",
      name: "New Mobile Test Landing V.7",
      component: () => import("../sites/lp2d/Index.vue"),
    },
    {
      path: "/lp2e",
      name: "New Mobile Test Landing V.8",
      component: () => import("../sites/lp2e/Index.vue"),
    },
    {
      path: "/lp7",
      name: "New Mobile Test Landing V.13",
      component: () => import("../sites/lp7/Index.vue"),
    },
    {
      path: "/lp7c",
      name: "New Mobile Test Landing V.14",
      component: () => import("../sites/lp7c/Index.vue"),
    },
    {
      path: "/lp8",
      name: "New Mobile Test Landing V.17",
      component: () => import("../sites/lp8/Index.vue"),
    },
    {
      path: "/lp2f",
      name: "New Mobile Test Landing V.18",
      component: () => import("../sites/lp2f/Index.vue"),
    },
    {
      path: "/lp9",
      name: "New Mobile Test Landing V.19",
      component: () => import("../sites/lp9/Index.vue"),
    },
    {
      path: "/lp11",
      name: "New Mobile Test Landing V.21",
      component: () => import("../sites/lp11/Index.vue"),
    },
    {
      path: "/lp12",
      name: "New Mobile Test Landing V.22",
      component: () => import("../sites/lp12/Index.vue"),
    },
    {
      path: "/lp12b",
      name: "New Mobile Test Landing V.23",
      component: () => import("../sites/lp12b/Index.vue"),
    },
    {
      path: "/lp13",
      name: "New Mobile Test Landing V.24",
      component: () => import("../sites/lp13/Index.vue"),
    },
    {
      path: "/lp2g",
      name: "New Mobile Test Landing V.25",
      component: () => import("../sites/lp2g/Index.vue"),
    },
    {
      path: "/lp14",
      name: "New Mobile Test Landing V.26",
      component: () => import("../sites/lp14/Index.vue"),
    },
    {
      path: "/lp12d",
      name: "New Mobile Test Landing V.27",
      component: () => import("../sites/lp12d/Index.vue"),
    },
    {
      path: "/lp9d",
      name: "New Mobile Test Landing V.28",
      component: () => import("../sites/lp9d/Index.vue"),
    },
    {
      path: "/lp7c",
      name: "New Mobile Test Landing V.32",
      component: () => import("../sites/lp7c/Index.vue"),
    },
    {
      path: "/lp8c",
      name: "New Mobile Test Landing V.33",
      component: () => import("../sites/lp8c/Index.vue"),
    },
    {
      path: "/lp9c",
      name: "New Mobile Test Landing V.34",
      component: () => import("../sites/lp9c/Index.vue"),
    },
    {
      path: "/lp11c",
      name: "New Mobile Test Landing V.35",
      component: () => import("../sites/lp11c/Index.vue"),
    },
    {
      path: "/lp12c",
      name: "New Mobile Test Landing V.36",
      component: () => import("../sites/lp12c/Index.vue"),
    },
    {
      path: "/lp13c",
      name: "New Mobile Test Landing V.37",
      component: () => import("../sites/lp13c/Index.vue"),
    },
    {
      path: "/lp14c",
      name: "New Mobile Test Landing V.38",
      component: () => import("../sites/lp14c/Index.vue"),
    },
    {
      path: "/lp4d",
      name: "New Mobile Test Landing V.39",
      component: () => import("../sites/lp4d/Index.vue"),
    },
    {
      path: "/lp7d",
      name: "New Mobile Test Landing V.40",
      component: () => import("../sites/lp7d/Index.vue"),
    },
    {
      path: "/lp8d",
      name: "New Mobile Test Landing V.41",
      component: () => import("../sites/lp8d/Index.vue"),
    },
    {
      path: "/lp11d",
      name: "New Mobile Test Landing V.42",
      component: () => import("../sites/lp11d/Index.vue"),
    },
    {
      path: "/lp13d",
      name: "New Mobile Test Landing V.43",
      component: () => import("../sites/lp13d/Index.vue"),
    },
    {
      path: "/lp14d",
      name: "New Mobile Test Landing V.44",
      component: () => import("../sites/lp14d/Index.vue"),
    },
    {
      path: "/lp2e",
      name: "New Mobile Test Landing V.45",
      component: () => import("../sites/lp2e/Index.vue"),
    },
    {
      path: "/lp4e",
      name: "New Mobile Test Landing V.46",
      component: () => import("../sites/lp4e/Index.vue"),
    },
    {
      path: "/lp7e",
      name: "New Mobile Test Landing V.47",
      component: () => import("../sites/lp7e/Index.vue"),
    },
    {
      path: "/lp8e",
      name: "New Mobile Test Landing V.48",
      component: () => import("../sites/lp8e/Index.vue"),
    },
    {
      path: "/lp9e",
      name: "New Mobile Test Landing V.49",
      component: () => import("../sites/lp9e/Index.vue"),
    },
    {
      path: "/lp11e",
      name: "New Mobile Test Landing V.50",
      component: () => import("../sites/lp11e/Index.vue"),
    },
    {
      path: "/lp12e",
      name: "New Mobile Test Landing V.51",
      component: () => import("../sites/lp12e/Index.vue"),
    },
    {
      path: "/lp13e",
      name: "New Mobile Test Landing V.52",
      component: () => import("../sites/lp13e/Index.vue"),
    },
    {
      path: "/lp14e",
      name: "New Mobile Test Landing V.53",
      component: () => import("../sites/lp14e/Index.vue"),
    },
    {
      path: "/lp2f",
      name: "New Mobile Test Landing V.54",
      component: () => import("../sites/lp2f/Index.vue"),
    },
    {
      path: "/lp4f",
      name: "New Mobile Test Landing V.55",
      component: () => import("../sites/lp4f/Index.vue"),
    },
    {
      path: "/lp7f",
      name: "New Mobile Test Landing V.56",
      component: () => import("../sites/lp7f/Index.vue"),
    },
    {
      path: "/lp8f",
      name: "New Mobile Test Landing V.57",
      component: () => import("../sites/lp8f/Index.vue"),
    },
    {
      path: "/lp9f",
      name: "New Mobile Test Landing V.58",
      component: () => import("../sites/lp9f/Index.vue"),
    },
    {
      path: "/lp11f",
      name: "New Mobile Test Landing V.59",
      component: () => import("../sites/lp11f/Index.vue"),
    },
    {
      path: "/lp12f",
      name: "New Mobile Test Landing V.60",
      component: () => import("../sites/lp12f/Index.vue"),
    },
    {
      path: "/lp13f",
      name: "New Mobile Test Landing V.61",
      component: () => import("../sites/lp13f/Index.vue"),
    },
    {
      path: "/lp14f",
      name: "New Mobile Test Landing V.62",
      component: () => import("../sites/lp14f/Index.vue"),
    },
    {
      path: "/lp2g",
      name: "New Mobile Test Landing V.63",
      component: () => import("../sites/lp2g/Index.vue"),
    },
    {
      path: "/lp4g",
      name: "New Mobile Test Landing V.64",
      component: () => import("../sites/lp4g/Index.vue"),
    },
    {
      path: "/lp7g",
      name: "New Mobile Test Landing V.65",
      component: () => import("../sites/lp7g/Index.vue"),
    },
    {
      path: "/lp8g",
      name: "New Mobile Test Landing V.66",
      component: () => import("../sites/lp8g/Index.vue"),
    },
    {
      path: "/lp9g",
      name: "New Mobile Test Landing V.67",
      component: () => import("../sites/lp9g/Index.vue"),
    },
    {
      path: "/lp11g",
      name: "New Mobile Test Landing V.68",
      component: () => import("../sites/lp11g/Index.vue"),
    },
    {
      path: "/lp11g",
      name: "New Mobile Test Landing V.69",
      component: () => import("../sites/lp11g/Index.vue"),
    },
    {
      path: "/lp12g",
      name: "New Mobile Test Landing V.70",
      component: () => import("../sites/lp12g/Index.vue"),
    },
    {
      path: "/lp13g",
      name: "New Mobile Test Landing V.71",
      component: () => import("../sites/lp13g/Index.vue"),
    },
    {
      path: "/lp14g",
      name: "New Mobile Test Landing V.72",
      component: () => import("../sites/lp14g/Index.vue"),
    },
    {
      path: "/lp15",
      name: "New Mobile Test Landing V.73",
      component: () => import("../sites/lp15/Index.vue"),
    },
    {
      path: "/lp15e",
      name: "New Mobile Test Landing V.74",
      component: () => import("../sites/lp15e/Index.vue"),
    },
    {
      path: "/lp15f",
      name: "New Mobile Test Landing V.75",
      component: () => import("../sites/lp15f/Index.vue"),
    },
    {
      path: "/lp15g",
      name: "New Mobile Test Landing V.76",
      component: () => import("../sites/lp15g/Index.vue"),
    },
    {
      path: "/lp15d",
      name: "New Mobile Test Landing V.77",
      component: () => import("../sites/lp15d/Index.vue"),
    },
    {
      path: "/lp15c",
      name: "New Mobile Test Landing V.78",
      component: () => import("../sites/lp15c/Index.vue"),
    },
    {
      path: "/lp10",
      name: "New Mobile Test Landing V.79",
      component: () => import("../sites/lp10/Index.vue"),
    },
    {
      path: "/splash",
      name: "New Mobile Test Landing V.80",
      component: () => import("../sites/splash/Index.vue"),
    },
    {
      path: "/lp16",
      name: "New Mobile Test Landing V.81",
      component: () => import("../sites/lp16/Index.vue"),
    },
    {
      path: "/lp17",
      name: "New Mobile Test Landing V.82",
      component: () => import("../sites/lp17/Index.vue"),
    },
    {
      path: "/lp17c",
      name: "New Mobile Test Landing V.83",
      component: () => import("../sites/lp17c/Index.vue"),
    },
    {
      path: "/lp17d",
      name: "New Mobile Test Landing V.84",
      component: () => import("../sites/lp17d/Index.vue"),
    },
    {
      path: "/lp17e",
      name: "New Mobile Test Landing V.85",
      component: () => import("../sites/lp17e/Index.vue"),
    },
    {
      path: "/lp17f",
      name: "New Mobile Test Landing V.86",
      component: () => import("../sites/lp17f/Index.vue"),
    },
    {
      path: "/lp17g",
      name: "New Mobile Test Landing V.87",
      component: () => import("../sites/lp17g/Index.vue"),
    },
  ],
});

export default router;
