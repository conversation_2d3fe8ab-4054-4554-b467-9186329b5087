<?php

class ExecutionTime {

    private $start  = 0;
    private $end    = 0;
    private $total  = 0;

    public function __construct(){
        $this->start = microtime(true);
    }

    public function stats(){
        $this->end      = microtime(true);
        return (object) [
            'start'     => $this->start,
            'end'       => $this->end,
            'duration'  => number_format($this->end - $this->start, 2)
        ];
    }

}

define('EXEC_TIME', new ExecutionTime());