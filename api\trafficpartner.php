<?php
include_once('./log.php');

$config = file_exists('./config/api.json') ? json_decode(file_get_contents('./config/api.json')) : null;

/**
 * Make sure the configuration is not missing or empty
 */
if($config == null){
	$fileMissing = json_encode([
		'code' => 401,
		'message' => 'Api configuration file missing',
	]);
	die($fileMissing);
}

$offer = $_GET['offer'];

$startTime	= microtime(true);

$data = [
		"ip"				=> $_SERVER['REMOTE_ADDR'],
		"email"				=> $_GET['email'],
		"gender"			=> $_GET['gender'],
		"searchGender"		=> $_GET['seek'],
		"birthdate"			=> $_GET['dob'],
		"username"			=> $_GET['username'],
		'password'			=> $_GET['password'],
		'landingUrl'		=> 'http://casualdating.com/?cid=14440cfe-3726-4b52-96ad-13c42d8046ec&clicktag='.$_GET['click_id'].'&zz=true&listId=3624&subPublisher='.$_GET['t1_t2'],
		'deviceType'		=> $_GET['device']['device'],
		'searchAgeFrom'		=> '22',
		'searchAgeTo'		=> '42',
		'userAgent'			=> $_GET['device']['raw']
];


$ch = curl_init($config->trafficpartner->$offer->endpoint);
curl_setopt_array($ch, array(
	CURLOPT_POST	=> true,
	CURLOPT_RETURNTRANSFER => true,
	CURLOPT_FOLLOWLOCATION => true,
	CURLOPT_HTTPHEADER => array(
		'Content-Type: application/json',
		'apiKey: '.$config->trafficpartner->$offer->apiKey,
		'apiToken: '.$config->trafficpartner->$offer->apiToken
	),
	CURLOPT_POSTFIELDS => json_encode($data)
));

$response = curl_exec($ch);
$responseData = json_decode($response, true);
$httpcode = curl_getinfo($ch, CURLINFO_RESPONSE_CODE);
curl_close($ch);

// Do not include Email in data storage
$data['email'] = '************';

$return = [
	'code' => 200,
	'message' => '',
	'url' => '',
	'time' => '0',
	'request_data' => $data,
	'response_data' => '',
	'source' => 'Trafficpartner ' . $offer,
	'offer' => $offer
];

/**
 * 1. Check if user created successfully - build success response
 * 2. If user creation failed - build error response
 */
if($response != false){
	if(is_array($responseData) && $responseData['success'] == false){
		$return['message'] = $responseData['reason'];
		$return['code'] = $httpcode;
	}else if($responseData['success'] == true){
		$return['message'] = 'Account successfully created';
		$return['url'] =$responseData['result']['loginUrl'];
	}
}else{
	$responseData = 'Unable to connect API';
}

$endTime = microtime(true);
$return['key']				= $_GET['key'];
$return['response_data']	= $responseData ? $responseData : $response;
$return['start_time']		= $startTime;
$return['end_time']			= $endTime;
$return['endpoint']			= $config->trafficpartner->$offer->endpoint;
$return['time'] 			= number_format( ( $endTime - $startTime ), 2);
$return['click_id']			= $_GET['click_id'];
$return['source_id']		= $_GET['source_id'];
$return['is_sellable']		= $_GET['is_sellable'];

$return['email']			= $_GET['email'];
$return['username']			= $_GET['username'];
$return['password']			= $_GET['password'];
$return['gender']			= $_GET['gender'];
$return['looking']			= $_GET['looking'];
$return['dob']				= $_GET['dob'];

$return['lander']			= $_GET['lander'];
$return['city']				= $_GET['city'];
$return['device']			= $_GET['device'];
$return['http']				= $_GET['http'];
$return['index_id']			= $_GET['index_id'];
$return['antifraud']		= isset($_GET['antifraud']) ? json_encode($_GET['antifraud']) : '';

// Send Data to Log API
$log	= new Log($return);
$send	= $log->send($config->log->endpoint);

echo json_encode($return);