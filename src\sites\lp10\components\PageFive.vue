<script setup>
import { ref, defineProps, inject } from "vue";
import Steps from "../../lp9/components/views/StepsMarker.vue";
import Params from "../../../assets/js/helper/urlParameters.js";
import Language from "../../../assets/js/helper/Language.js";
import Searching from "../../../sites/lp12/components//views/Searching.vue";

const props = defineProps(["steps", "moveNextSlide", "moveBackSlide", "language", "location", "inputs"]);
const steps = ref(props.steps);
const selectedAge = ref(null);
const assets = inject("assets");

const validationMessages = ref({ dob: null });

const dob = (x, y) => {
  let min = parseInt(x);
  let max = parseInt(y);
  let random = Math.floor(Math.random() * (parseInt(max) - parseInt(min))) + min;
  props.inputs.birth_year.value = new Date().getFullYear() - (random + 1);
  props.inputs.birth_month.value = (Math.floor(Math.random() * (parseInt(12) - parseInt(1))) + 1).toString();
  props.inputs.birth_day.value = (Math.floor(Math.random() * (parseInt(29) - parseInt(1))) + 1).toString();

  props.inputs.birth_day.value.length == 1 ? (props.inputs.birth_day.value = "0" + props.inputs.birth_day.value) : "";
  props.inputs.birth_month.value.length == 1 ? (props.inputs.birth_month.value = "0" + props.inputs.birth_month.value) : "";

  props.moveNextSlide();
};
</script>

<template>
  <div class="container my-5">
    <div class="card p-4">
      <div class="header-logo">
        <img src="../../../assets/flirty.png" class="logofli" alt="" />
      </div>
      <form>
        <div class="form-group">
          <label for="gender" class="w-100 text-center fs-5">{{ Language.age_question }}</label>
          <hr />
          <div class="d-flex justify-content-around my-3 mt-5 gap-3">
            <button type="button" class="btn border btn-outline-12 rounded-5 px-4 18-btn" for="option2" @click="dob(18, 34)">18+</button>
            <button type="button" class="btn border btn-outline-12 rounded-5 px-4 35-btn" for="option2" @click="dob(35, 40)">35+</button>
            <button type="button" class="btn border btn-outline-12 rounded-5 px-4 40-btn" for="option2" @click="dob(40, 64)">40+</button>
            <button type="button" class="btn border btn-outline-12 rounded-5 px-4 65-btn" for="option2" @click="dob(65, 75)">65+</button>
          </div>
        </div>
      </form>
      <p v-if="validationMessages.dob" class="text-light fs-6 mt-2">({{ validationMessages.dob }})</p>
      <Steps :step="steps" />
      <div class="disclaimer mt-5">
        <div class="progress mb-3" role="progressbar" aria-label="Info example" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100">
          <div class="progress-bar bg-info" style="width: 70%"></div>
        </div>
        <div class="d-flex justify-content-center">
          <Searching :location="location" :language="Language" />
        </div>
      </div>
    </div>
  </div>
</template>

<style>
body {
  background-color: #f0f8ff;
}
.card {
  max-width: 400px;
  margin: auto;
  padding: 20px;
  border-radius: 15px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}
.header-logo {
  text-align: center;
  margin-bottom: 20px;
}
.header-logo img {
  width: 150px;
}
.header-text {
  text-align: center;
  font-size: 1.5rem;
  margin-bottom: 30px;
}
.form-group {
  margin-bottom: 15px;
}
.btn-primary {
  background-color: #6a1b9a;
  border-color: #6a1b9a;
  font-size: 1.2rem;
}
.disclaimer {
  text-align: center;
  margin-top: 20px;
}
.disclaimer small {
  color: #6c757d;
}

.btn-outline-12 {
  border-color: #6a1b9a;
  color: black;
}

.btn-outline-12:hover {
  background-color: #6a1b9a;
  color: white;
}

.styled-paragraph {
  font-style: italic;
  font-size: 1rem;
  color: palevioletred !important;
}

.card {
  height: 30em;
  width: 25em;
}
</style>
