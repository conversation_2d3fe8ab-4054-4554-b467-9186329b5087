<?php
include_once('./log.php');

$config = file_exists('./config/api.json') ? json_decode(file_get_contents('./config/api.json')) : null;

/**
 * Make sure the configuration is not missing or empty
 */
if($config == null){
	$fileMissing = json_encode([
		'code' => 401,
		'message' => 'Api configuration file missing',
	]);
	die($fileMissing);
}

$startTime	= microtime(true);

$offer = $_GET['offer'];

$data = [
	'countryCode'	=> $_GET['device']['geo']['country_code'],
	'email' 		=> $_GET['email'],
	'gender'		=> $_GET['gender'],
	'dateOfBirth'	=> $_GET['dob'],
	'username' 		=> $_GET['username'],
	'password' 		=> $_GET['password'],
	'ip'			=> $_GET['device']['ip'],
	"city"			=> $_GET['device']['geo']['city'],
	"userAgent"		=> $_GET['device']['raw'],
	"ref"			=> $_GET['click_id'],
	"sub1"			=> $_GET['t1'],
	"sub2"			=> $_GET['t2'],
	"sub3"			=> $_GET['image']
];


$ch = curl_init($config->datingstars->endpoint);
curl_setopt_array($ch, array(
	CURLOPT_POST	=> true,
	CURLOPT_RETURNTRANSFER => true,
	CURLOPT_FOLLOWLOCATION => true,
	CURLOPT_SSL_VERIFYHOST => false,
	CURLOPT_SSL_VERIFYPEER => false,
	CURLOPT_HTTPHEADER => array(
		'Content-Type: application/json',
		'Authorization: Basic '. base64_encode("{$config->datingstars->auth->username}:{$config->datingstars->auth->password}")
	),
	CURLOPT_POSTFIELDS => json_encode($data)
));

$response = curl_exec($ch);
$responseData = json_decode($response, true);
$httpcode = curl_getinfo($ch, CURLINFO_RESPONSE_CODE);
curl_close($ch);

// Do not include Email in data storage
$data['email'] = '************';

$return = [
	'code' => 200,
	'message' => '',
	'url' => '',
	'time' => '0',
	'request_data' => $data,
	'response_data' => '',
	'source' => 'Datingstars ' . $offer,
	'offer' => $offer
];

/**
 * 1. Check if user created successfully - build success response
 * 2. If user creation failed - build error response
 */
if($response != false){
	if(is_array($responseData) && array_key_exists('errors', $responseData)){
		$return['message'] = $responseData['errors'];
		$return['code'] = $httpcode;
	}else{
		if($httpcode == 201 && array_key_exists('status', $responseData)){
			$return['message'] = 'Account successfully created';
			$return['url'] = $responseData['redirectUrl'];
		}else{
			$return['message'] = $responseData['error'];
			$return['code'] = $httpcode;
		}
	}
}else{
	$responseData = 'Unable to connect API';
}

$endTime = microtime(true);
$return['key']				= $_GET['key'];
$return['response_data']	= $responseData ? $responseData : $response;
$return['start_time']		= $startTime;
$return['end_time']			= $endTime;
$return['endpoint']			= $config->datingstars->endpoint;
$return['time'] 			= number_format( ( $endTime - $startTime ), 2);
$return['click_id']			= $_GET['click_id'];
$return['source_id']		= $_GET['t1'] . '_' . $_GET['t2'];
$return['is_sellable']		= $_GET['is_sellable'];

$return['email']			= $_GET['email'];
$return['username']			= $_GET['username'];
$return['password']			= $_GET['password'];
$return['gender']			= $_GET['gender'];
$return['looking']			= $_GET['looking'];
$return['dob']				= $_GET['dob'];

$return['city']				= $_GET['device']['geo']['city'];
$return['lander']			= $_GET['lander'];
$return['device']			= $_GET['device']['device'];
$return['http']				= $_GET['http'];
$return['index_id']			= $_GET['index_id'];
$return['antifraud']		= isset($_GET['antifraud']) ? json_encode($_GET['antifraud']) : '';


// Send Data to Log API
$log	= new Log($return);
$send	= $log->send($config->log->endpoint);

echo json_encode($return);