import axios from 'axios'
import endpoints from '../config/endpoints.json'
import params from '../helper/urlParameters.js'
import device from '../helper/deviceDetection.js'

const datingstars = async (formData) => {

    const httpData = {
        username            : formData.username,
        password            : formData.password,
        email               : formData.email,
        gender              : formData.gender,
        looking             : formData.seek,
        location            : formData.location,
        dob                 : formData.birth_year + "-" + formData.birth_month + "-" + formData.birth_day,
        click_id            : formData.click_id,
        key                 : formData.key, // Unique Identifier for Logger
        is_sellable         : !formData.is_sellable,
        lander              : formData.lander,
        image               : params().image,
        device              : formData.device,
        http                : formData.http,
        offer               : "au",
        t1                  : params().t1,
        t2                  : params().t2,
        index_id            : "145",
        traf_id             : formData.traf_id,
        antifraud   : formData.antifraud,
    }

    return await axios.get(endpoints.datingstars, { params: httpData })
}

export default datingstars