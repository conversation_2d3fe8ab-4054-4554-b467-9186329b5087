<?php
include_once('./log.php');
include_once('./call.php');

$config = file_exists('./config/api.json') ? json_decode(file_get_contents('./config/api.json')) : null;

$startTime = microtime(true);


/**
 * Make sure the configuration is not missing or empty
 */
if($config == null){
    $fileMissing = json_encode([
        'code' => 401,
        'message' => 'Api configuration file missing',
    ]);
    die($fileMissing);
}

$traf_id = $_GET['traf_id'] ? $_GET['traf_id'] : "";
$md5Hash = md5($traf_id);
$traf_id = substr($md5Hash, 0, 16);

$offer = $_GET['offer'] ? $_GET['offer'] : 'us';

$data = [
    'param' => '1',
    'p'     => $config->mirelia->offers->$offer->p,
    'ip'    => $_GET['device']['ip'],
    'cid'   => $_GET['click_id'],
    'adwpl' => $traf_id,
    'email' => $_GET['email']
];

$return = [
    'code' => 200,
    'message' => '',
    'url' => '',
    'time' => '0',
    'source' => 'mirelia - ' . $offer
];


$key = $config->mirelia->offers->$offer->key;
$params = http_build_query($data);
$signature = hash_hmac('sha256', $params, $key);
$post_data = $params."&sig=".$signature;


$ch = curl_init($config->mirelia->offers->$offer->endpoint);

curl_setopt_array($ch, array(
    CURLOPT_POST    => true,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_FOLLOWLOCATION => true,
    CURLOPT_POSTFIELDS => $post_data
));

$response = curl_exec($ch);
$responseData = json_decode($response, true);
$httpcode = curl_getinfo($ch, CURLINFO_RESPONSE_CODE);
curl_close($ch);

// Do not include Email in data storage
$data['account']['emailAddress'] = '************';

$return = [
    'code' => 200,
    'message' => '',
    'url' => '',
    'time' => '0',
    'request_data' => $data,
    'response_data' => '',
    'source' => 'Mirelia '.$offer
];

/**
 * 1. Check if user created successfully - build success response
 * 2. If user creation failed - build error response
 */

$response = (array)json_decode($response);

if($response != false){
    if($response['status'] == 'success' || $response['status'] == 'success_rec'){
        $return['message']  = 'Account successfully created';
        $return['url']      = $response['url'];
        $return['code']     = 200;
    }else{
        $return['message']  = $responseData['message'];
        $return['code']     = 400;
    }
}else{
    $response = 'Unable to connect API';
}

$_GET['looking'] = $_GET['seek'];

$endTime = microtime(true);
$return['key']              = $_GET['key'];
$return['response_data']    = json_encode($response);
$return['start_time']       = $startTime;
$return['end_time']         = $endTime;
$return['endpoint']         = $config->mirelia->offers->$offer->endpoint;
$return['time']             = number_format( ( $endTime - $startTime ), 2);
$return['click_id']         = $_GET['click_id'];
$return['source_id']        = $_GET['source_id'];
$return['is_sellable']      = $_GET['is_sellable'];

$return['email']            = $_GET['email'];
$return['username']         = $_GET['username'];
$return['password']         = $_GET['password'];
$return['gender']           = $_GET['gender'];
$return['looking']          = $_GET['looking'];
$return['dob']              = $_GET['dob'];

$return['device']           = $_GET['device'];
$return['city']             = $_GET['city'];
$return['lander']           = $_GET['lander'];
$return['http']             = $_GET['http'];
$return['index_id']			= $_GET['index_id'];
$return['antifraud']		= isset($_GET['antifraud']) ? json_encode($_GET['antifraud']) : '';

// Send Data to Log API
$log    = new Log($return);
$send   = $log->send($config->log->endpoint);

echo json_encode($return);
