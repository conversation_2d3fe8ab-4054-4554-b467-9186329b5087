<?php
include_once('./log.php');

$config = file_exists('./config/api.json') ? json_decode(file_get_contents('./config/api.json')) : null;

/**
 * Make sure the configuration is not missing or empty
 */
if($config == null){
	$fileMissing = json_encode([
		'code' => 401,
		'message' => 'Api configuration file missing',
	]);
	die($fileMissing);
}

$startTime = microtime(true);

$md5Hash = $_GET['traf_id'] ? md5($_GET['traf_id']) : "";
$traf_id = $md5Hash ? substr($md5Hash, 0, 16) : ""; 

$traf_id = $traf_id ? $traf_id . "_" : "";
$sub2 = $traf_id . $_GET['source_id'];

$data = [
	'api_key'				=> $config->hoi_new->api_key,
	'username' 				=> $_GET['username'],
	'email' 				=> $_GET['email'],
	'password' 				=> $_GET['password'],
    'password_confirmation' => $_GET['password'],
	'profile' => [
		'birthday' 	=> $_GET['birthdate'],
		'gender'	=> $_GET['gender'],
		'city'		=> $_GET['city'],
		'zip_code'	=> $_GET['zip']
	],
    'sub1'					=> $_GET['click_id'], // Click ID Generated by us
	'sub2'					=> $sub2,
	'sub3'					=> $_GET['s3']
];



$ch = curl_init($config->hoi_new->endpoint);
curl_setopt_array($ch, array(
	CURLOPT_POST	=> true,
	CURLOPT_RETURNTRANSFER => true,
	CURLOPT_FOLLOWLOCATION => true,
	CURLOPT_HTTPHEADER => array(
		'Content-Type: application/json',
		'Accept: application/json'
	),
	CURLOPT_POSTFIELDS => json_encode($data)
));

$response = curl_exec($ch);
$responseData = json_decode(trim($response), true);
$httpcode = curl_getinfo($ch, CURLINFO_RESPONSE_CODE);
curl_close($ch);

// Do not include Email in data storage
$data['email'] = '************';

$return = [
	'code' => 200,
	'message' => '',
	'url' => '',
	'time' => '0',
	'request_data' => $data,
	'response_data' => '',
	'source' => 'HOI New'
];


/**
 * 1. Check if user created successfully - build success response
 * 2. If user creation failed - build error response
 */
if($response != false){
	if($responseData == NULL){
		$return['message'] = 'Dead API Link Or Incorrect Key';
		$return['code'] = 404;
	}else if(isset($responseData['errors'])){
		foreach($responseData['errors'] as $error){
			$return['message'] = $error[0];
			$return['code'] = $httpcode;
		}	
	}else if(isset($responseData['message'])){
		$return['message'] = $responseData['message'];
		$return['code'] = $httpcode;
	}else{
		$return['message'] = 'Account successfully created';
		$return['url'] = $responseData['redirect_url'];
	}
}else{
	$responseData = 'Unable to connect API';
}

if($_GET['valid_geo'] != $_GET['device']['geo']['country_code']){
	$return['message'] .= " - Invalid GEO";
	$return['code']		= 400;
}

$endTime = microtime(true);
$return['key']				= $_GET['key'];
$return['response_data']	= $responseData;
$return['start_time']		= $startTime;
$return['end_time']			= $endTime;
$return['endpoint']			= $config->hoi_new->endpoint;
$return['time'] = number_format( ( $endTime - $startTime ), 2);
$return['click_id']			= $_GET['click_id'];
$return['source_id']		= $_GET['source_id'];
$return['is_sellable']		= $_GET['is_sellable'];

$return['email']			= $_GET['email'];
$return['username']			= $_GET['username'];
$return['password']			= $_GET['password'];
$return['gender']			= $_GET['gender'];
$return['looking']			= $_GET['looking'];
$return['dob']				= $_GET['birthdate'];

$return['device']			= $_GET['device'];
$return['lander']			= $_GET['lander'];
$return['http']				= $_GET['http'];
$return['city']				= $_GET['city'];
$return['index_id']			= $_GET['index_id'];
$return['antifraud']		= isset($_GET['antifraud']) ? json_encode($_GET['antifraud']) : '';
// Send Data to Log API
$log	= new Log($return);
$send	= $log->send($config->log->endpoint);

echo json_encode($return);