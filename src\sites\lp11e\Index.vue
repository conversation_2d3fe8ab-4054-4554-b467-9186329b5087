<script setup>
import { ref, onMounted, provide, watch } from "vue";
import Axios from "axios";
import PageOne from "../lp11e/components/PageOne.vue";
import PageTwo from "../lp11e/components/PageTwo.vue";
import PageThree from "../lp11e/components/PageThree.vue";
import PageFour from "../lp11e/components/PageFour.vue";
import PageFive from "../lp11e/components/PageFive.vue";
import PageSix from "../lp11e/components/PageSix.vue";
import PageSeven from "../lp11e/components/PageSeven.vue";
import PageEight from "../lp11e/components/PageEight.vue";
import Navbar from "../../components/Navbar.vue";
import Footer from "../../components/Footer9.vue";
import Background from "../../components/Background.vue";
import Endpoints from "./../../assets/js/config/endpoints.json";
import Params from "./../../assets/js/helper/urlParameters.js";
import Media from "./../../assets/js/helper/deviceDetection.js";
import Language from "./../../assets/js/helper/Language.js";
import inputData from "./../../assets/js/config/forms.json";
import config from "../../assets/js/config/config.json";

const location = ref();
const device = ref(false);
const steps = ref(1);
const hasEmailParam = ref(false);
const emailValidated = ref(false);

provide("assets", config.assets);

onMounted(async () => {
  const urlParams = new URLSearchParams(window.location.search);
  const emailParam = urlParams.get("email");

  hasEmailParam.value = !!emailParam;
  if (hasEmailParam.value) {
    inputData.email = {
      value: emailParam,
      valid: true,
      error: false,
      required: true,
    };
  }

  await Axios.get(Endpoints.geo).then((s) => {
    device.value = s.data;
    inputData.location.value = s.data.geo.city ? s.data.geo.city : "Nevada";
    inputData.device.value = s.data;
    (inputData.lander.value = Params().path), (inputData.click_id.value = Params().click_id);
    inputData.source_id.value = Params().source_id;
    inputData.country.value = s.data.geo.country;
    inputData.country_code.value = s.data.geo.country_code.toLowerCase();
    (inputData.locale.value = Params().locale), (inputData.media.value = Media().device);
    location.value = inputData.location.value;
    inputData.http.value = Params().http;
    inputData.t1.value = Params().t1;
    inputData.t2.value = Params().t2;
    inputData.l.value = Params().l;
    inputData.s3.value = Params().s3;
    inputData.cmp.value = Params().cmp;
    inputData.traf_id.value = Params().traf_id;
inputData.tsid.value = Params().tsid;
inputData.alreadyRegistered.value = Params().alreadyRegistered;
    if (s.data.geo.postal_code) {
      inputData.postal_code.value = s.data.geo.postal_code;
    }
  });
  window.history.pushState({ page: 1 }, null);
  window.addEventListener("popstate", handlePopstate);
});

function handlePopstate() {
  window.location.href = config.links.back_button + Params().url;
}

const moveNextSlide = () => {
  if (hasEmailParam.value && steps.value === 5) {
    steps.value = 7;
    return;
  }

  const userAge = calculateAge();
  if (userAge < 40) {
    switch (steps.value) {
      case 1:
        steps.value = 2;
        break;
      case 2:
        steps.value = 3;
        break;
      case 3:
        steps.value = 4;
        break;
      case 4:
        steps.value = 7;
        break;
      case 7:
        steps.value = 8;
        break;
      default:
        steps.value++;
    }
  } else {
    steps.value++;
  }
};

const calculateAge = () => {
  const birthYear = parseInt(inputData.birth_year.value);
  const birthMonth = parseInt(inputData.birth_month.value);
  const birthDay = parseInt(inputData.birth_day.value);

  const today = new Date();
  let age = today.getFullYear() - birthYear;
  const monthDiff = today.getMonth() + 1 - birthMonth;

  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDay)) {
    age--;
  }

  return age;
};

const moveBackSlide = () => {
  if (hasEmailParam.value && steps.value === 7) {
    steps.value = 5;
    return;
  }

  const userAge = calculateAge();
  if (userAge < 40) {
    switch (steps.value) {
      case 8:
        steps.value = 7;
        break;
      case 7:
        steps.value = 4;
        break;
      case 4:
        steps.value = 3;
        break;
      case 3:
        steps.value = 2;
        break;
      case 2:
        steps.value = 1;
        break;
      default:
        steps.value--;
    }
  } else {
    steps.value--;
  }
};

watch(steps, (newValue) => {
  console.log("Step changed to:", newValue);
});

watch(
  () => inputData.email.valid,
  (newVal) => {
    if (newVal && steps.value === 6) {
      emailValidated.value = true;
    }
  }
);
</script>

<template>
  <div class="layoutlp9">
    <Background />
    <Navbar />
    <div class="content-container d-flex justify-content-center" style="height: 100vh">
      <div class="bg-transparent">
        <div v-if="steps === 1">
          <PageOne :inputs="inputData" :steps="steps" :moveNextSlide="moveNextSlide" :moveBackSlide="moveBackSlide" :location="location" />
        </div>
        <div v-else-if="steps === 2">
          <PageTwo :inputs="inputData" :steps="steps" :moveNextSlide="moveNextSlide" :moveBackSlide="moveBackSlide" :location="location" />
        </div>
        <div v-else-if="steps === 3">
          <PageThree :inputs="inputData" :steps="steps" :moveNextSlide="moveNextSlide" :moveBackSlide="moveBackSlide" :location="location" />
        </div>
        <div v-else-if="steps === 4">
          <PageFive :inputs="inputData" :steps="steps" :moveNextSlide="moveNextSlide" :moveBackSlide="moveBackSlide" :location="location" />
        </div>
        <div v-else-if="steps === 5 && calculateAge() >= 40">
          <PageFour :inputs="inputData" :steps="steps" :moveNextSlide="moveNextSlide" :moveBackSlide="moveBackSlide" :location="location" />
        </div>
        <div v-else-if="steps === 6 && calculateAge() >= 40">
          <PageSix :inputs="inputData" :steps="steps" :moveNextSlide="moveNextSlide" :moveBackSlide="moveBackSlide" :location="location" :emailValidated="emailValidated" />
        </div>
        <div v-else-if="steps === 7">
          <PageSeven :inputs="inputData" :steps="steps" :moveNextSlide="moveNextSlide" :moveBackSlide="moveBackSlide" :location="location" :showPassword="calculateAge() >= 40" :showEmail="calculateAge() < 40" />
        </div>
        <div v-else-if="steps === 8">
          <PageEight :inputs="inputData" :steps="steps" :moveNextSlide="moveNextSlide" :moveBackSlide="moveBackSlide" :location="location" />
        </div>
      </div>
    </div>
    <Footer :language="Language" class="mt-2" />
  </div>
</template>

<style>
.layoutlp9 {
  position: relative;
  overflow: hidden;
}

.content-container {
  position: relative;
  z-index: 1;
}

@media only screen and (max-width: 867px) {
  .lp8x {
    height: auto !important;
    margin-bottom: 20px !important;
  }
}
</style>
