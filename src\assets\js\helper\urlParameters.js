import { ref } from 'vue'
import Services from '../../js/config/services.json'

const urlParameters = () => {

    const   url = decodeURI(window.location.href)
    const   hashes = url.split('?')[1]
    
    let     urlString       = hashes ? '?' : ''
    let     raw             = []

    if(hashes){
        const   hash = hashes.split('&')
        const   count = Object.keys(hash).length
        let     counter = 1

        for(let u in hash){
            urlString += hash[u] + (count > counter ? '&' : '')
            counter++
            let keyValuePair = hash[u].split('=')
            raw[keyValuePair[0]] = keyValuePair[1]
        }
    }

    // Service selector
    let selectedServices = []
    let serviceSelector = raw.indexOf('select') ? raw.select : ''
    let jData = ''
    if( (serviceSelector != ''  && /\[.*\]/i.test(serviceSelector)) || serviceSelector != ''  && /{.*}/i.test(serviceSelector) ){

        if(serviceSelector.includes('[')){
            jData = serviceSelector.replace('[', '').replace(']', '')
        }else if(serviceSelector.includes('{')){
            jData = serviceSelector.replace('{', '').replace('}', '')
        }

        jData = jData.split(',')
        for(let x in jData){
            if(jData[x] in Services){
                selectedServices.push(Services[jData[x]])
            }
        }
        //selectedServices.push('Smartlink')
    }
    // else{
    //     selectedServices.push('Smartlink')
    // }

    // Window update the decode URL
    window.history.replaceState({}, document.title, urlString);

    // Get browser language/locale
    const locale = () => {
        const lang = ref('')
        if (navigator.languages && navigator.languages.length) {
          lang.value = navigator.languages[0];
        } else {
          lang.value = navigator.userLanguage || navigator.language || navigator.browserLanguage || 'en';
        }
        // take the first two letter only
        if(lang.value.length > 2){
            let explode = lang.value.split('-')
            lang.value = explode[0]
        }
        return lang.value
    }

    // Get folder path
    const path = () => {
        let path = window.location.pathname;
        return path
    }
    
    return {
        'url'           : urlString,
        'affiliate'     : 'a' in raw ? raw['a'] : '',
        'offer'         : 'o' in raw ? raw['o'] : '',
        'image'         : 'image' in raw ? raw['image'] : '',
        'click_id'      : 'click_id' in raw ? raw['click_id'] : '',
        'traf_id'       : 'traf_id' in raw ? raw['traf_id'] : '',
        't1'            : 't1' in raw ? raw['t1'] : '',
        't2'            : 't2' in raw ? raw['t2'] : '',
        's3'            : 't2' in raw ? raw['t2'] : '',
        'cmp'           : raw['t1'] + '_' + raw['t2'],
        'strict'        : 'strict' in raw > 0 ? raw['strict'] : 'true',
        'source_id'     : 't1' in raw > 0 ? raw['t1'] : '',
        'locale'        : locale(),
        'path'          : path(),
        'l'             : raw['l'], // Offer Region
        'sm_response'   : 'sm_response' in raw ? raw['sm_response'] : '',
        /**
         * API Selector
         * Select to which API it should send data
         * If select is in used, it is exclusive call
         * */
        'alreadyRegistered' : 'alreadyRegistered' in raw ? raw['alreadyRegistered'] : '',
        'tsid' : 'tsid' in raw ? raw['tsid'] : '',
        'select'        : selectedServices,

        'raw'           : raw,
        'http'          : window.location.href,
        'ipCheck'       : raw['ipCheck'],
        'eCheck'        : raw['eCheck']
    }
    
}

export default urlParameters