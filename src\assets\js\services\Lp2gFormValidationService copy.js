import axios from "axios";
import Swal from "sweetalert2";
import Params from "../helper/urlParameters";

export const validateLp2Form = async (event, props, options = {}) => {
  const { showSearching, disableSubmitBtn, apiErrorMessage, validatePrivacy, validateEmail, validateUsername, validateDOB, validatePassword, rules, Language, Api, calculateAge } = options;

  // Check privacy checkbox first
  if (!validatePrivacy()) {
    Swal.fire({
      icon: "error",
      title: "Privacy Policy Required",
      text: Language.alert_update,
      confirmButtonText: "OK",
    });
    return false;
  }

  // For users under 35, validate email
  if (props.showEmail && !validateEmail()) {
    Swal.fire({
      icon: "error",
      title: "Invalid Email",
      text: Language.email_error,
      confirmButtonText: "OK",
    });
    return false;
  }

  // GTM event
  window.dataLayer?.push({
    event: "button_click",
    Click_Classes: "button_click",
    _event: "gtm.linkClick",
    _triggers: "203887914_4",
    "gtm.element": event.target,
    "gtm.elementClasses": "button_click g-recaptcha btn border btn-outline-12 rounded-5 px-5",
    "gtm.elementId": "submit-button",
  });

  const userAge = calculateAge();

  // Rest of validation based on age
  if (userAge >= 35) {
    if (!validateUsername() || !validateDOB() || !validateEmail() || !validatePassword()) {
      return false;
    }
  } else if (!validateDOB()) {
    return false;
  }

  // Continue with form submission if all validation passes
  showSearching.value = true;
  disableSubmitBtn.value = true;

  // Prepare form data
  let formData = {};
  for (let x in props.inputs) {
    formData[x] = props.inputs[x]["value"];
  }

  try {
    if (userAge < 35) {
      const urlParams = new URLSearchParams(window.location.search);
      const currentLander = formData.lander || window.location.pathname;
      const data = {
        email: formData.email,
        antifraud: {
          FraudLogix: {},
          Reoon: {},
          Outlook: {},
        },
      };
      // RTB Data preparation
      const rtbData = new URLSearchParams();
      const addToParams = (prefix, obj) => {
        Object.keys(obj).forEach((key) => {
          if (typeof obj[key] === "object" && obj[key] !== null) {
            addToParams(`${prefix}[${key}]`, obj[key]);
          } else {
            rtbData.append(`${prefix}[${key}]`, obj[key]);
          }
        });
      };

      // Add rules data based on country code
      const countryCode = formData.device?.geo?.country_code;
      if (countryCode && rules.value) {
        rtbData.append("data[rules][tier]", rules.value.tier);
        rtbData.append("data[rules][price]", rules.value.price);
        rtbData.append("data[rules][supported][exoclick]", true);
        rtbData.append("data[rules][supported][loudbids]", true);
      } else {
        rtbData.append("data[rules][tier]", "3");
        rtbData.append("data[rules][price]", "");
        rtbData.append("data[rules][supported][exoclick]", true);
        rtbData.append("data[rules][supported][loudbids]", true);
      }

      // Add the antifraud data
      rtbData.append("data[username]", formData.username || "");
      rtbData.append("data[password]", formData.password || "");
      rtbData.append("data[email]", formData.email || "");
      rtbData.append("data[gender]", formData.gender || "male");
      rtbData.append("data[birth_year]", formData.birth_year);
      rtbData.append("data[birth_month]", formData.birth_month);
      rtbData.append("data[birth_day]", formData.birth_day);
      rtbData.append("data[location]", formData.location || "");
      rtbData.append("data[ip]", formData.device?.ip || "");
      rtbData.append("data[lander]", currentLander);
      rtbData.append("data[t1]", urlParams.get("t1") || "");
      rtbData.append("data[t2]", urlParams.get("t2") || "");
      rtbData.append("data[key]", Date.now().toString());
      rtbData.append("data[click_id]", urlParams.get("click_id") || "");
      rtbData.append("data[is_sellable]", formData.is_sellable ? 1 : 0);
      rtbData.append("data[privacy]", formData.privacy ? 1 : 0);
      rtbData.append("data[seek]", formData.seek);
      rtbData.append("data[http]", formData.http);
      rtbData.append("data[source_id]", urlParams.get("t1") || "");
      rtbData.append("data[locale]", formData.locale ? formData.locale : "");
      rtbData.append("data[tsid]", urlParams.get("tsid") || "");
      rtbData.append("data[traf_id]", urlParams.get("traf_id") || "");

      const deviceData = typeof formData.device === "string" ? JSON.parse(formData.device) : formData.device || {};
      addToParams("data[device]", deviceData);

      // Initialize validation flags
      let rtb = true;

      // Start validation checks
      try {
        // Check for outlook email first
        if (formData.email?.toLowerCase().includes("@outlook.com")) {
          rtb = false;
          data.antifraud.Outlook = "TRUE";
        } else if (rules.value?.validate) {
          // IP Check
          try {
            const allowedTrafIds = ["109", "108", "128", "121"];
            const trafId = String(Params()["traf_id"]);
            if (!allowedTrafIds.includes(trafId)) {
              const fraudlogixResponse = await axios.get("https://sexymeetups.com/api/fraudlogix.php", {
                params: { ip: formData.device?.ip },
              });
              data.antifraud.FraudLogix = fraudlogixResponse.data;
              data.isSafeIP = fraudlogixResponse.data.status === 1;
            }
            // Modified validation check
            if ((data.isSafeIP == true || Params()["ipCheck"] == "false") && Params()["eCheck"] != "false") {
              // Email Check
              if (formData.email) {
                try {
                  const reoonEndpoint = `https://emailverifier.reoon.com/api/v1/verify?email=${formData.email}&key=KtXXvWJgP3zghCo6hjwg5UlYiyuOvzOl&mode=power`;
                  const reoonResponse = await axios.get(reoonEndpoint);

                  data.antifraud.Reoon = reoonResponse.data;
                  data.isSafeEmail = reoonResponse.data.overall_score >= 75;

                  if (!data.isSafeEmail) {
                    rtb = false;
                  }
                } catch (error) {
                  console.error("Email verification error:", error);
                  rtb = false;
                }
              }
            } else {
              rtb = false;
            }
          } catch (error) {
            console.error("IP check error:", error);
            rtb = false;
          }
        }
      } catch (error) {
        console.error("Validation check error:", error);
        rtb = false;
      }

      rtbData.append("data[antifraud]", JSON.stringify(data.antifraud));
      // Try RTB if validations passed
      if (rtb) {
        try {
          const rtbResponse = await axios.get(`/api/rtb.php?${rtbData.toString()}`);

          if (rtbResponse.data?.url) {
            window.location.href = rtbResponse.data.url;
            return true;
          } else {
            rtb = false; // Force smartlink if no URL in response
          }
        } catch (error) {
          console.error("RTB Error:", error);
          rtb = false;
        }
      }

      // Always try Smartlink if RTB failed or validation failed
      const smartlinkData = new URLSearchParams({
        username: "",
        password: "",
        email: formData.email || "",
        sex: formData.gender || "male",
        birthdate: `${formData.birth_year}-${String(formData.birth_month).padStart(2, "0")}-${String(formData.birth_day).padStart(2, "0")}`,
        ip: formData.device?.ip || "",
        var1: `${urlParams.get("t1") || ""}_${urlParams.get("t2") || ""}`,
        t1_t2: `${urlParams.get("t1") || ""}_${urlParams.get("t2") || ""}`,
        click_id: urlParams.get("click_id") || "",
        source_id: urlParams.get("t1") || "",
        key: Date.now().toString(),
        gender: formData.gender || "male",
        looking: "female",
        dob: `${formData.birth_year}-${String(formData.birth_month).padStart(2, "0")}-${String(formData.birth_day).padStart(2, "0")}`,
        device: formData.device?.type,
        lander: currentLander,
        city: formData.device?.geo?.city,
        is_sellable: "0",
        http: window.location.href,
        antifraud: JSON.stringify(data.antifraud),
        browser: formData.device?.browser || navigator.userAgent.match(/chrome|firefox|safari|opera|edge|msie/i)?.[0] || "",
        os: formData.device?.os,
        country: formData.device?.geo?.country_code,
        tsid: urlParams.get("tsid") || "",
        select: urlParams.get("select") || "",
        source: urlParams.get("source") || "",
        subsource: urlParams.get("subsource") || "",
        traf_id: urlParams.get("traf_id") || "",
      });

      try {
        // Get the tsid from URL parameters
        const tsid = urlParams.get("tsid");
        if (!tsid) {
          throw new Error("TSID not found in URL parameters");
        }

        // Construct the endpoint URL with the tsid
        const endpoint = `https://trakle01.online/tracker/${tsid}`;

        const smartlinkResponse = await axios.get(`/api/smartlink.php?${smartlinkData.toString()}&endpoint=${encodeURIComponent(endpoint)}`);

        if (smartlinkResponse.data?.url) {
          // Construct final URL with all parameters
          const finalUrl = new URL(smartlinkResponse.data.url);
          ["tsid", "select", "source", "subsource", "traf_id", "t1", "t2", "click_id"].forEach((param) => {
            const value = urlParams.get(param);
            if (value) {
              finalUrl.searchParams.append(param, value);
            }
          });
          window.location.href = finalUrl.toString();
          return true;
        } else {
          // If no URL in response, redirect to endpoint with parameters
          const fallbackUrl = new URL(endpoint);
          ["select", "source", "subsource", "traf_id", "t1", "t2", "click_id"].forEach((param) => {
            const value = urlParams.get(param);
            if (value) {
              fallbackUrl.searchParams.append(param, value);
            }
          });
          window.location.href = fallbackUrl.toString();
          return true;
        }
      } catch (error) {
        console.error("Smartlink Error:", error);
        apiErrorMessage.value = true;
      }
    } else {
      await Api(formData, disableSubmitBtn, apiErrorMessage);
    }
  } catch (error) {
    console.error("Form submission error:", error.response?.data || error);
    apiErrorMessage.value = true;
  } finally {
    disableSubmitBtn.value = false;
    showSearching.value = false;
  }

  return true;
};
