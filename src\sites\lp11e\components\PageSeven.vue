<script setup>
import { ref, defineProps, onMounted } from "vue";
import Steps from "../../lp9/components/views/StepsMarker.vue";
import Language from "../../../assets/js/helper/Language.js";
import Api from "../../../assets/js/helper/api.js";
import Swal from "sweetalert2";
import Searching from "../../lp14/components/views/Searching.vue";
import { validateLp2Form } from "@/assets/js/services/Lp2eFormValidationService";

const props = defineProps({
  inputs: Object,
  steps: Number,
  moveNextSlide: Function,
  moveBackSlide: Function,
  location: String,
  showPassword: Boolean,
  showEmail: Boolean,
});

const steps = ref(props.steps);
const apiErrorMessage = ref(false);
const disableSubmitBtn = ref(false);
const showSearching = ref(false);
const rules = ref(null);
const validationMessages = ref({
  username: false,
  dob: false,
  email: false,
  password: false,
  privacy: false,
  privacy_service: false,
});

onMounted(async () => {
  window.dataLayer?.push({
    event: "page_view",
  });

  const uncheckedCountries = ["AT", "BE", "BG", "HR", "CY", "CZ", "DK", "EE", "FI", "FR", "DE", "GR", "HU", "IE", "IT", "LV", "LT", "LU", "MT", "NL", "PL", "PT", "RO", "SK", "SI", "ES", "SE", "AD", "BA", "IS", "LI", "MC", "MD", "ME", "MK", "NO", "RS", "CH", "UA", "GB", "VA", "SM"];

  if (props.inputs.device.value?.geo?.country_code && !uncheckedCountries.includes(props.inputs.device.value.geo.country_code)) {
    props.inputs.privacy.value = true;

    const privacyCheckbox = document.getElementById("privacyCheck");
    if (privacyCheckbox) {
      privacyCheckbox.checked = true;
    }
  }

  // Load rules dynamically based on country code
  const countryCode = props.inputs.device.value?.geo?.country_code;
  if (countryCode) {
    try {
      rules.value = await import(`../../../assets/js/config/antifraud/rules/${countryCode}.json`);
    } catch (e) {
      console.warn(`No rules found for country ${countryCode}, using default rules`);
      rules.value = { tier: "3", price: "" };
    }
  }
});

const calculateAge = () => {
  const birthYear = parseInt(props.inputs.birth_year.value);
  const birthMonth = parseInt(props.inputs.birth_month.value);
  const birthDay = parseInt(props.inputs.birth_day.value);

  const today = new Date();
  let age = today.getFullYear() - birthYear;
  const monthDiff = today.getMonth() + 1 - birthMonth;

  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDay)) {
    age--;
  }

  return age;
};

const validateForm = async (event) => {
  // Check privacy checkbox first
  if (!validatePrivacy()) {
    Swal.fire({
      icon: "error",
      title: "Privacy Policy Required",
      text: Language.alert_update,
      confirmButtonText: "OK",
    });
    return false;
  }

  // For users under 35, validate email
  if (props.showEmail && !validateEmail()) {
    Swal.fire({
      icon: "error",
      title: "Invalid Email",
      text: Language.email_error,
      confirmButtonText: "OK",
    });
    return false;
  }

  return validateLp2Form(event, props, {
    showSearching,
    disableSubmitBtn,
    apiErrorMessage,
    validatePrivacy,
    validateEmail,
    validateUsername,
    validateDOB,
    validatePassword,
    rules,
    Language,
    Api,
    calculateAge,
  });
};

const validatePrivacy = () => {
  const isValid = props.inputs.privacy.value === true;
  validationMessages.value.privacy = isValid ? false : Language.alert_update;
  return isValid;
};

const validatePassword = () => {
  let x = props.inputs.password.value;
  if (x.length > 14 || x.length < 6 || validator(x) == false || explicitValidator(x)) {
    validationMessages.value.password = Language.password_error_2;
    return false;
  } else {
    validationMessages.value.password = false;
    return true;
  }
};

const urlParams = new URLSearchParams(window.location.search);
const isUsernameZero = urlParams.get("username") === "all";

const validateUsername = () => {
  let x = props.inputs.username.value;
  const usernameRegex = /^(?=.*[0-9])(?=.*[a-zA-Z]).{6,14}$/;
  const usernameSimpleRegex = /^.{6,14}$/;

  if (isUsernameZero) {
    if (!usernameSimpleRegex.test(x)) {
      validationMessages.value.username = Language.username_error_3;
      return false;
    } else {
      validationMessages.value.username = false;
      return true;
    }
  } else {
    if (x.length > 14 || x.length < 6 || validator(x) == false || explicitValidator(x)) {
      validationMessages.value.username = Language.username_error_2;
      return false;
    } else {
      validationMessages.value.username = false;
      return true;
    }
  }
};

const validateEmail = () => {
  const emailRegex = /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,})+$/;
  const isValid = emailRegex.test(props.inputs.email.value);
  validationMessages.value.email = isValid ? false : Language.email_error;
  return isValid;
};

const validator = (str) => {
  return /[0-9][a-zA-Z]|[a-zA-Z][0-9]/.test(str);
};

const explicitValidator = (str) => {
  const format = /[ `!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~]/;
  return format.test(str);
};

const validateDOB = () => {
  if (props.inputs.birth_day.value == "" || props.inputs.birth_year.value == "" || props.inputs.birth_month.value == "") {
    validationMessages.value.dob = Language.select_your_age;
    return false;
  } else {
    validationMessages.value.dob = false;
    return true;
  }
};
</script>

<template>
  <Searching v-if="showSearching" />

  <div class="container-fluid d-flex align-items-center justify-content-center min-vh-100">
    <div class="text-center">
      <div v-if="props.showEmail">
        <p class="fs-1 mb-5">{{ Language.email_text }}</p>
        <div class="d-flex justify-content-center mb-4">
          <input v-model="props.inputs.email.value" @blur="validateEmail" @keyup.enter="continueToNextSlide" class="form-control rounded-5" style="width: 25em" type="email" id="email" placeholder="<EMAIL>" @keypress="handleKeyPress" />
        </div>
      </div>
      <p class="fs-1 mb-5 create" v-if="props.showPassword">{{ Language.password_create }}</p>
      <div class="d-flex justify-content-center gap-5 mb-5 button-container">
        <input v-model="inputs.password.value" v-if="props.showPassword" class="form-control rounded-5" style="width: 25em; height: 6em" type="password" id="password" placeholder="Enter Your Password" @keypress="handleKeyPress" />
        <button type="button" class="btn final-submit-btn g-recaptcha btn-danger rounded-5 fs-5" style="height: 5em; width: 20em" @click="(event) => validateForm(event)" :class="`${disableSubmitBtn ? 'disabled' : ''}`">
          {{ Language.btn_submit }}
          <div class="spinner-border spinner-grow-sm text-light" v-show="disableSubmitBtn" role="status">
            <span class="visually-hidden">{{ Language.loading }}</span>
          </div>
        </button>
      </div>

      <!-- <div class="d-flex justify-content-center gap-5 mb-5 button-container">
        <input v-model="inputs.password.value" class="form-control rounded-5" style="width: 25em; height: 6em" type="password" id="password" placeholder="Enter Your Password" @keypress="handleKeyPress" />
        <button type="button" class="btn g-recaptcha btn-danger rounded-5 fs-5" style="height: 5em; width: 20em" :class="`${disableSubmitBtn ? 'disabled' : ''}`" role="button" data-sitekey="6LcVHV4qAAAAAOz2INgLc2XPKtP5OdnJTktATdPS" data-callback="onSubmit" data-action="submit">
          {{ Language.btn_submit }}
          <div class="spinner-border spinner-grow-sm text-light" v-show="disableSubmitBtn" role="status">
            <span class="visually-hidden">{{ Language.loading }}</span>
          </div>
        </button>
      </div> -->
      <div class="others">
        <!-- <p>{{ Language.password_error_2 }}</p> -->
        <div class="d-flex agreement flex-column align-items-center justify-content-center">
          <div class="text-center" style="width: 35em !important">
            <div class="d-flex flex-column align-items-center justify-content-center">
              <div class="notice">
                <p class="agreement">
                  <strong> {{ Language.basic_info }}</strong> <br />
                  <strong>{{ Language.data_controller }}</strong> {{ Language.leads }} <a class="cursor-pointer text-danger" href="/privacy">{{ Language.info }}</a> <br />
                  <strong>{{ Language.purpose }}</strong> {{ Language.data_response }} <a class="cursor-pointer text-danger" href="/privacy">{{ Language.info }}</a> <br />
                  <strong>{{ Language.rights }}</strong
                  >{{ Language.data_access }} <a class="cursor-pointer text-danger" href="/privacy">{{ Language.info }}</a> <br />
                  <strong>{{ Language.additional_info }}</strong
                  >{{ Language.data_protect }} <a class="cursor-pointer text-danger" href="/privacy">{{ Language.privacy_policy }}</a>
                </p>
              </div>
              <div class="accept_agreed agreement">
                <div class="d-flex align-items-start mb-1">
                  <input type="checkbox" id="privacyCheck" v-model="inputs.privacy.value" class="custom-checkbox mt-1" />
                  <label for="privacyCheck" class="agree text-dark ms-2">{{ Language.privacy_agree }}</label>
                </div>
                <!-- <div class="d-flex align-items-start mb-1">
                  <input type="checkbox" id="serviceCheck" v-model="inputs.privacy_service.value" class="custom-checkbox mt-1" />
                  <label for="serviceCheck" class="agree text-dark ms-2">{{ Language.privacy_service_agree }}</label>
                </div> -->
                <!-- <div class="d-flex align-items-start mb-1">
                  <input type="checkbox" id="promotionCheck" v-model="inputs.promotion_agree.value" class="custom-checkbox mt-1" />
                  <label for="promotionCheck" class="agree text-dark ms-1">{{ Language.privacy_promotion_agree }}</label>
                </div> -->
              </div>
            </div>
          </div>
          <div
            v-if="
              ['Los Angeles', 'San Diego', 'Santa Monica', 'San Francisco', 'San Jose', 'Fresno', 'Sacramento', 'Long Beach', 'Oakland', 'Bakersfield', 'Anaheim', 'Santa Ana', 'Riverside', 'Stockton', 'Chula Vista', 'Irvine', 'Fremont', 'San Bernardino', 'Modesto', 'Oxnard', 'Fontana'].includes(
                inputs.location.value
              )
            "
            class="text-center mt-3"
          >
            <input type="checkbox" id="flexCheckDefault" v-model="inputs.is_sellable.value" />
            <label class="fs-6 ms-2" for="flexCheckDefault">{{ Language.data_permission }}</label>
          </div>
        </div>
      </div>
      <div class="text-center mt-2">
        <p class="text-dark" style="font-size: 10px">{{ Language.spam_alert }}</p>
      </div>
      <div class="steps d-flex gap-3 justify-content-center mt-3">
        <i class="fa-solid fa-star fs-3 text-white"></i>
        <i class="fa-solid fa-star fs-3 text-white"></i>
        <i class="fa-solid fa-star fs-3 text-white"></i>
        <i class="fa-solid fa-star fs-3 text-white"></i>
        <i class="fa-solid fa-star fs-3 text-white"></i>
        <i class="fa-solid fa-star fs-3 text-white"></i>
        <i class="fa-solid fa-star fs-3 text-white"></i>
      </div>
    </div>
    <div id="alert-message3" style="display: none">
      {{ Language.alert_update }}
    </div>
    <div id="alert-message" style="display: none">
      {{ Language.alert_update }}
    </div>
    <div id="alert-message2" style="display: none">
      {{ Language.password_error_2 }}
    </div>
    <Steps :step="steps" />
  </div>
</template>

<style scoped>
.notice {
  font-size: 8px !important;
}

.ms-1 {
  margin-right: 19px !important;
}

.accept_agreed .form-check-input {
  width: 20px; /* Set a fixed width */
  height: 20px; /* Set a fixed height */
}

.accept_agreed {
  font-size: 13px !important;
  text-align: start !important;
}
@media (max-width: 768px) {
  .fs-1 {
    font-size: 1.5rem;
  }
  .button-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-bottom: 4px !important;
    gap: 2; /* Adjust as needed */
  }
  .form-control {
    width: 20em !important;
    height: 4em !important;
  }
  .btn {
    width: 16em !important;
    height: 3em !important;
  }
  .steps {
    margin-top: 1em;
  }
  .others {
    width: 22em !important;
  }
  .agreement {
    width: 22em !important;
    margin-left: 1.5em !important;
    font-size: 13px !important;
    text-align: start !important;
  }
}
</style>
