<?php
include_once('./log.php');

$config = file_exists('./config/api.json') ? json_decode(file_get_contents('./config/api.json')) : null;

/**
 * Make sure the configuration is not missing or empty
 */
if($config == null){
	$fileMissing = json_encode([
		'code' => 401,
		'message' => 'Api configuration file missing',
	]);
	die($fileMissing);
}

$offer = $_GET['offer'];

$startTime	= microtime(true);

$data = [
	"username"			=> $_GET['username'],
	"password"			=> $_GET['password'],
	"email"				=> $_GET['email'],
	"birthday"			=> $_GET['dob'],
	'acceptTerms'		=> 0,
	"city"				=> $_GET['city'],
	"country"			=> $_GET['country'],
	"zip"				=> $_GET['zip'],
	'gender'			=> $_GET['gender'] == 'male' ? 1 : 2,
	'device'			=> $_GET['device']['device'],
	'var1'				=> $_GET['t1_t2'],
	'campaign_id'		=> '24dcc6b0',
    'var4'		        => $_GET['click_id'],
];

$ch = curl_init($config->leadpartner->endpoint->$offer);
curl_setopt_array($ch, array(
	CURLOPT_POST	=> true,
	CURLOPT_RETURNTRANSFER => true,
	CURLOPT_FOLLOWLOCATION => true,
	CURLOPT_POSTFIELDS => $data
));

$response = curl_exec($ch);
$responseData = json_decode($response, true);
$httpcode = curl_getinfo($ch, CURLINFO_RESPONSE_CODE);
curl_close($ch);

// Do not include Email in data storage
$data['email'] = '************';

$return = [
	'code' => 200,
	'message' => '',
	'url' => '',
	'time' => '0',
	'request_data' => $data,
	'response_data' => '',
	'source' => 'Leadpartner ' . $offer,
	'offer' => $offer
];

/**
 * 1. Check if user created successfully - build success response
 * 2. If user creation failed - build error response
 */
if($response != false){
	if(isset($responseData['errors'])){
		$return['code'] = $httpcode;
		foreach($responseData['errors'] as $key => $val){
			$return['message'] = $responseData['errors'][$key][0];
		}
	}else if(isset($responseData['data'])){
		$return['message'] = 'Account successfully created';
		$return['url'] = @$responseData['redirect_url'];
	}
}else{
	$responseData = 'Unable to connect API';
}

$endTime = microtime(true);
$return['key']				= $_GET['key'];
$return['response_data']	= $responseData ? $responseData : $response;
$return['start_time']		= $startTime;
$return['end_time']			= $endTime;
$return['endpoint']			= $config->leadpartner->endpoint->$offer;

$return['time'] 			= number_format( ( $endTime - $startTime ), 2);
$return['click_id']			= $_GET['click_id'];
$return['source_id']		= $_GET['source_id'];
$return['is_sellable']		= $_GET['is_sellable'];

$return['email']			= $_GET['email'];
$return['username']			= $_GET['username'];
$return['password']			= $_GET['password'];
$return['gender']			= $_GET['gender'];
$return['looking']			= $_GET['looking'];
$return['dob']				= $_GET['dob'];

$return['city']				= $_GET['city'];
$return['lander']			= $_GET['lander'];
$return['device']			= $_GET['device'];
$return['http']				= $_GET['http'];
$return['index_id']			= $_GET['index_id'];
$return['antifraud']		= isset($_GET['antifraud']) ? json_encode($_GET['antifraud']) : '';


// Send Data to Log API
$log	= new Log($return);
$send	= $log->send($config->log->endpoint);

echo json_encode($return);