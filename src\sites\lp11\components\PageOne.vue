<script setup>
import { ref, defineProps } from "vue";
import Steps from "../../lp9/components/views/StepsMarker.vue";
import Searching from "../../../components/Searching.vue";
import Language from "../../../assets/js/helper/Language.js";

const props = defineProps(["steps", "moveNextSlide", "moveBackSlide", "language", "location"]);
const steps = ref(props.steps);

const selectPreference = (preference) => {
  props.moveNextSlide(preference);
};
</script>

<template>
  <div class="container-fluid d-flex align-items-center justify-content-center min-vh-100">
    <div class="text-center">
      <p class="fs-1 mb-5">{{ Language.over_18 }}</p>
      <div class="d-flex justify-content-center gap-5 mb-5 button-container">
        <button type="button" class="btn btn-outline-light rounded-5 fs-5" style="height: 5em; width: 16em !important" @click="selectPreference('no')">{{ Language.no }}</button>
        <button type="button" class="btn btn-danger rounded-5 fs-5" style="height: 5em; width: 16em !important" @click="selectPreference('yes')">{{ Language.yes_i_am }}</button>
      </div>
      <div class="steps d-flex gap-3 justify-content-center mb-3">
        <i class="fa-solid fa-star fs-3 text-white"></i>
        <i class="fa-regular fa-star fs-3 text-white"></i>
        <i class="fa-regular fa-star fs-3 text-white"></i>
        <i class="fa-regular fa-star fs-3 text-white"></i>
        <i class="fa-regular fa-star fs-3 text-white"></i>
        <i class="fa-regular fa-star fs-3 text-white"></i>
        <i class="fa-regular fa-star fs-3 text-white"></i>
      </div>
      <Searching :location="location" :language="Language" />
    </div>
    <Steps :step="steps" />
  </div>
</template>

<style scoped>
@media (max-width: 768px) {
  .fs-1 {
    font-size: 1.5rem;
  }
  .button-container {
    flex-direction: column;
  }
  .btn {
    width: 100%;
    height: 3em !important;
  }
  .steps {
    margin-top: 1em;
  }
}
</style>
