<?php
header('Access-Control-Allow-Origin: *');
$config = file_exists('./config/api.json') ? json_decode(file_get_contents('./config/api.json')) : die('Invalid Config');

$ch = curl_init();
curl_setopt_array($ch, array(
	CURLOPT_RETURNTRANSFER  => true,
	CURLOPT_FOLLOWLOCATION  => true,
    CURLOPT_SSL_VERIFYHOST  => 0,
    CURLOPT_SSL_VERIFYPEER  => 0,
	CURLOPT_HTTPHEADER      => array(
		'x-api-key: ' .$config->fraudlogix->api_key,
		'Content-Type: application/json'
	),
	CURLOPT_URL             => $config->fraudlogix->endpoint. '?ip='. $_GET['ip']
));


$curl_request = curl_exec($ch);
$response = json_decode($curl_request, true);
$httpcode = curl_getinfo($ch, CURLINFO_RESPONSE_CODE);
curl_close($ch);

$scores = ['Low', 'Medium'];
$return = $response;

if(is_array($response) && isset($response['RiskScore'])){
    if(!in_array($response['RiskScore'], $scores) || $response['VPN'] == true || $response['AbnormalTraffic'] == true){
        $return['status'] = 0;
    }else{
        $return['status'] = 1;
    }
}else{
    $return['status'] = 0;
}

header('Content-Type: application/json; charset=utf-8');
echo json_encode($return);