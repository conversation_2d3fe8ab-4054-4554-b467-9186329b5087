<script setup>
import { defineProps, ref } from "vue";

const props = defineProps(["location", "language"]);
const city = ref("");
const loading = ref(10);

const inteval = setInterval(function () {
  if (props.location != null && props.location.length > 0) {
    city.value = props.location ? props.location : " your area";
    clearInterval(inteval);
  }
}, 3500);

const progress = setInterval(function () {
  loading.value += 8;
  if (city.value) {
    clearInterval(progress);
  }
}, 100);

// Fixed number of girls set to 52
const found = 52;
</script>

<template>
  <div class="text-center">
    <div class="mb-1" v-if="!city">
      <div class="text-white fs-3"><i class="fa-solid text-dark fa-location-dot"></i></div>
      <div class="searching fs-6 text-white fw-light">{{ language.gps_text_3 }}</div>
      <!-- 
            <div class="progress mx-auto col-10" role="progressbar" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100" style="height: 4px;">
                <div class="progress-bar progress-bar-striped progress-bar-animated bg-warning" :style="{ width: loading +'%'}"></div>
            </div>
            -->
    </div>
    <div class="mb-1" v-else>
      <div class="text-white fs-3"><i class="fa-solid text-dark fa-venus"></i></div>
      <div class="searching fs-6 text-white fw-light">{{ found }} {{ language.gps_text_2 }} {{ city }}</div>
    </div>
  </div>
</template>

<style scoped>
.searching {
  font-size: 0.98em !important;
}
</style>
