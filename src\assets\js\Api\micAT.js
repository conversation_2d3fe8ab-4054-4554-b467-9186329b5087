import axios from 'axios'
import endpoints from '../config/endpoints.json'
import { ref } from 'vue'

const talon = ref('')

const micat = async (formData) => {

talon.value = document.getElementById('talon').value; 

const httpData = {
    key                 : formData.key,
    http                : formData.http,
    click_id            : formData.click_id,
    device              : formData.device,
    cmp                 : formData.cmp,
    nickname            : formData.username,
    email               : formData.email,
    password            : formData.password,
    source_id           : formData.source_id,
    is_sellable         : !formData.is_sellable,
    lander              : formData.lander,
    dob                 : formData.birth_year + "-" + formData.birth_month + "-" + formData.birth_day,
    offer               : "at",
    gender              : formData.gender,
    looking             : formData.seek,
    talon               : talon.value,
    city                : formData.location,
    index_id            : "40",
    traf_id             : formData.traf_id,
    antifraud           : formData.antifraud,
}

    return await axios.get(endpoints.mic, { params: httpData })
}

export default micat