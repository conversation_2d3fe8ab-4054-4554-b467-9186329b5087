import axios from 'axios'
import endpoints from '../config/endpoints.json'
import params from '../helper/urlParameters.js'

const loudbids = async (formData) => {    

    console.log(formData)

    const httpData = {
        username    : formData.username,
        email       : formData.email,
        password    : formData.password,
        birthyear   : formData.birth_year,
        gender      : formData.gender,
        city        : formData.location,
        zip         : formData.postal_code,
        country     : formData.country_code,
        click_id    : formData.click_id,
        key         : formData.key,
        source_id   : formData.source_id,
        is_sellable : !formData.is_sellable,
        device      : formData.device,
        lander      : formData.lander,
        http        : formData.http,
        s3          : formData.s3,
        looking     : formData.seek,
        traf_id     : formData.traf_id,
        index_id    : "214",
        offer       : "us",
        t1          : formData.t1,
        locale      : formData.locale,
        antifraud   : formData.antifraud,
    }

    return await axios.get(endpoints.loudbids, { params: httpData })
}

export default loudbids