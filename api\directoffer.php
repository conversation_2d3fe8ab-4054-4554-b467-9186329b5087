<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1); 
include_once('./log.php');

$config = file_exists('./config/api.json') ? json_decode(file_get_contents('./config/api.json')) : null;

/**
 * Make sure the configuration is not missing or empty
 */
if($config == null){
	$fileMissing = json_encode([
		'code' => 401,
		'message' => 'Api configuration file missing',
	]);
	die($fileMissing);
}

$startTime	= microtime(true);

$data = [
	'username' 	=> isset($_GET['username']) && !empty($_GET['username']) ? $_GET['username'] : '',
	'password' 	=> isset($_GET['password']) && !empty($_GET['password']) ? $_GET['password'] : '',
	'email' 	=> isset($_GET['email']) && !empty($_GET['email']) ? $_GET['email'] : '',
	'gender'	=> isset($_GET['gender']) && !empty($_GET['gender']) ? $_GET['gender'] : '',
	'seek'		=> isset($_GET['seek']) && !empty($_GET['seek']) ? $_GET['seek'] : '',
	'tsid'		=> isset($_GET['tsid']) && !empty($_GET['tsid']) ? $_GET['tsid'] : '',
	'ip'		=> isset($_GET['device']['ip']) && !empty($_GET['device']['ip']) ? $_GET['device']['ip'] : '',
	'click_id'	=> isset($_GET['sub_id']) && !empty($_GET['sub_id']) ? $_GET['sub_id'] : '',
];

$endpoint = $config->directoffer->endpoint;

$url = $endpoint . '?' . http_build_query($data);
$ch = curl_init();

$raw = isset($_GET['device']['raw']) && !empty($_GET['device']['raw']) ? $_GET['device']['raw'] : '';

curl_setopt_array($ch, [
    CURLOPT_URL => $url,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_FOLLOWLOCATION => true,
    CURLOPT_TIMEOUT => 10,
    CURLOPT_USERAGENT => $raw,
    CURLOPT_SSLVERSION => CURL_SSLVERSION_TLSv1_2,
    CURLOPT_VERBOSE => true,
    CURLOPT_HTTPHEADER => [
        'Content-Type: application/json',
    ],
]);

$response = curl_exec($ch);
$responseData = json_decode($response, true);


// Do not include Email in data storage
$data['email'] = '************';

$return = [
	'code' => 200,
	'message' => '',
	'url' => '',
	'time' => '0',
	'request_data' => $data,
	'response_data' => '',
	'source' => 'DirectOffer ',
	'offer' => ''
];

/**
 * 1. Check if user created successfully - build success response
 * 2. If user creation failed - build error response
 */
if($responseData != false){
	if($responseData['message'] == 'Success'){
		$return['message'] = $responseData['message'];
		$return['code'] = '200';
		$return['url'] = $responseData['url'];
	}else{
		$return['message'] = $responseData['message'];
		$return['code'] = $responseData['status_code'];
	}
}else{
	$responseData = 'Unable to connect API';
}

$endTime = microtime(true);
$return['key']				= $_GET['key'];
$return['response_data']	= $responseData ? $responseData : $response;
$return['start_time']		= $startTime;
$return['end_time']			= $endTime;
$return['endpoint']			= $endpoint;
$return['time'] 			= number_format( ( $endTime - $startTime ), 2);
$return['click_id']			= $_GET['sub_id'];
$return['source_id']		= $_GET['aff_id'];
$return['is_sellable']		= $_GET['is_sellable'];

$return['email']			= $_GET['email'];
$return['username']			= $_GET['username'];
$return['password']			= $_GET['password'];
$return['gender']			= $_GET['gender'];
$return['looking']			= $_GET['seek'];
$return['dob']				= $_GET['birth_year'].'-'.$_GET['birth_month'].'-'.$_GET['birth_day'];

$return['city']				= $_GET['city'];
$return['lander']			= $_GET['lander'];
$return['device']			= $_GET['device'];
$return['http']				= $_GET['http'];
$return['index_id']			= $_GET['index_id'];
$return['antifraud']		= isset($_GET['antifraud']) ? json_encode($_GET['antifraud']) : '';


// Send Data to Log API
$log	= new Log($return);
$send	= $log->send($config->log->endpoint);

echo json_encode($return);