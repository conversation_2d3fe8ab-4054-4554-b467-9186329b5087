import axios from 'axios'
import endpoints from '../config/endpoints.json'

const trafficpartnerus = async (formData) => {

    const httpData = {
        email               : formData.email,
        gender              : formData.gender,
        username            : formData.username,
        password            : formData.password,
        dob                 : formData.birth_year+'-'+formData.birth_month+'-'+formData.birth_day,
        source_id           : formData.source_id,
        click_id            : formData.click_id,
        key                 : formData.key,
        is_sellable         : !formData.is_sellable,
        device              : formData.device,
        lander              : formData.lander,
        http                : formData.http,
        seek                : formData.seek,
        t1_t2               : formData.cmp,
        offer               : 'us',
        looking             : formData.seek,
        city                : formData.location,
        index_id            : "21",
        traf_id             : formData.traf_id,
        antifraud   : formData.antifraud,
    };

    return await axios.get(endpoints.trafficpartner, { params: httpData })
}

export default trafficpartnerus