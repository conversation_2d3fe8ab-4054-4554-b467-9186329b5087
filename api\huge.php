<?php
include_once('./log.php');

$config = file_exists('./config/api.json') ? json_decode(file_get_contents('./config/api.json')) : null;

$startTime = microtime(true);

/**
 * Make sure the configuration is not missing or empty
 */
if($config == null){
	$fileMissing = json_encode([
		'code' => 401,
		'message' => 'Api configuration file missing',
	]);
	die($fileMissing);
}

$data = [
    'acode' => $config->huge_traffic->predefined_form->acode,
	'em' => $_GET['email'],
	'uip' => $_SERVER['REMOTE_ADDR'],
	'gender' => ucwords(substr($_GET['gender'], 0,1)),
	'username' => $_GET['username'],
	'byear' => $_GET['birth_year'],
	'bmonth' => $_GET['birth_month'],
	'bday' => $_GET['birth_day'],
	'ad_id' => $_GET['click_id'], // Click ID
	'mobile' => $_GET['mobile'],
	'cmp' => $_GET['cmp'] //$_GET['source_id']
];

$ch = curl_init($config->huge_traffic->endpoint);
curl_setopt_array($ch, array(
	CURLOPT_POST	=> true,
	CURLOPT_RETURNTRANSFER => true,
	//CURLOPT_FOLLOWLOCATION => true,
	CURLOPT_POSTFIELDS => $data
));

$response = curl_exec($ch);
$responseData = explode('|', $response);
$httpcode = curl_getinfo($ch, CURLINFO_RESPONSE_CODE);
curl_close($ch);

// Do not include Email in data storage
$data['em'] = '************';

$return = [
	'code' => 200,
	'message' => '',
	'url' => '',
	'time' => '0',
	'request_data' => $data,
	'response_data' => '',
	'source' => 'Huge Traffic'
];

/**
 * 1. Check if user created successfully - build success response
 * 2. If user creation failed - build error response
 */
if($response != false){
	if(is_array($responseData) && count($responseData) > 0){
		$status = trim($responseData[0], '\n\n');
		if($responseData[0] == "0"){
			$return['code'] = 200;
			$return['message'] = trim($responseData[0]);
			$return['url'] = $responseData[3];
		}else{
			$return['code'] = 402;
			$return['message'] = $responseData[1];
		}
	}
}else{
	$responseData = 'Unable to connect API';
}


$endTime = microtime(true);
$return['key']				= $_GET['key'];
$return['response_data']	= $responseData;
$return['start_time']		= $startTime;
$return['end_time']			= $endTime;
$return['endpoint']			= $config->huge_traffic->endpoint;
$return['time']				= number_format( ( $endTime - $startTime ), 2);
$return['click_id']			= $_GET['click_id'];
$return['source_id']		= $_GET['cmp'];
$return['is_sellable']		= $_GET['is_sellable'];

$return['email']			= $_GET['email'];
$return['username']			= $_GET['username'];
$return['password']			= $_GET['password'];
$return['gender']			= $_GET['gender'];
$return['looking']			= $_GET['looking'];
$return['dob']				= $_GET['birth_year'].'-'.$_GET['birth_month'].'-'.$_GET['birth_day'];

$return['device']			= $_GET['device'];
$return['city']				= $_GET['city'];
$return['lander']			= $_GET['lander'];
$return['http']				= $_GET['http'];
$return['index_id']			= $_GET['index_id'];
$return['antifraud']		= isset($_GET['antifraud']) ? json_encode($_GET['antifraud']) : '';

// Send Data to Log API
$log	= new Log($return);
$send	= $log->send($config->log->endpoint);

echo json_encode($return);