<?php
include_once('./log.php');
include_once('./helpers/execution_time.php');

class ExoClick {


	private $config;
	private $data;
	private $return;
	private $geoISO;
	private $params;
	private $site_config;
	private $execStats;

	private $bidRequestCode;
	private $bidPlacementCode;
	private $bidRedirectionUrl;
	private $bidPlacementUrl;
	
	public function __construct() {
		$this->config	= file_exists('./config/api.json') ? json_decode(file_get_contents('./config/api.json')) : null;
		
		if($this->config == null){
			$fileMissing = json_encode([
				'code' => 401,
				'message' => 'Api configuration file missing',
			]);
			die($fileMissing);
		}
	}

	
	public function getBidPrice($params) {

		$this->params		= $params;
		$this->geoISO		= $params->device->geo->country_code;
		$device				= $params->device;
		$this->site_config	= isset($this->config->RTB->ExoClick->{$params->device->geo->country_code}) ? $this->config->RTB->ExoClick->{$params->device->geo->country_code} : $this->config->RTB->ExoClick->WW;
		$traf_id			= isset($params->traf_id) ? substr(md5($params->traf_id), 0, 16) : "";
		$s1					= !empty($traf_id) && isset($params->source_id) ? $traf_id . "_" . $params->source_id : $traf_id;
		
		$this->data = [
			"id" => "d4b5c697-41f3-4c1c-a3d5-5fd01b5ef2aa",
			"imp" => [
				[
					"id"    		=> "974090632",
					"instl" 		=> 0,
					"el"    		=> base64_encode($params->email)
				]
			],
			"site" => [
				"id"        => $this->site_config->id,
				"domain"    => $this->site_config->domain,
				"name"      => $this->site_config->name,
				"page"      => $this->site_config->page
			],
			"device"    => [
				"ua"        => $device->raw,
				"ip"        => $device->ip,
				"language"  => $params->locale,
				"os"        => $device->os
			],
			"user"  => [
				"id"    => $params->click_id
			]
		];

		if(isset($params->traf_id)){
			$this->data['ext']['sub'] = $params->traf_id;
		}

		// Add Tier 3 countries
		if(isset($this->params->rules->price) && !is_null($this->params->rules->price) && (float)$this->params->rules->price > 0){
			$this->data['imp'][0]['bidfloor']		= $params->rules->price;
			$this->data['imp'][0]['bidfloorcur']	= "USD";
		}

		return $this->initiateBidPriceCall();
	}

	
	private function initiateBidPriceCall() {

		$ch = curl_init($this->site_config->endpoint);
		curl_setopt_array($ch, array(
			CURLOPT_POST    => true,
			CURLOPT_RETURNTRANSFER => true,
			CURLOPT_FOLLOWLOCATION => true,
			CURLOPT_SSL_VERIFYHOST => false,
			CURLOPT_SSL_VERIFYPEER => false,
			CURLOPT_HTTPHEADER => array(
				'x-openrtb-version: 2.4',
				'Connection: Keep-Alive',
				'Authorization: Bearer '.$this->site_config->api_key,
				'Content-Type: application/json'
			),
			CURLOPT_POSTFIELDS => json_encode($this->data)
		));

		$response					= curl_exec($ch);
		$responseData				= json_decode($response, TRUE);
		$this->bidRequestCode		= curl_getinfo($ch, CURLINFO_RESPONSE_CODE);
		curl_close($ch);

		if($this->bidRequestCode == 200){
			$this->bidPlacementUrl		= isset($responseData['seatbid'][0]['bid'][0]['nurl']) ? $responseData['seatbid'][0]['bid'][0]['nurl'] : '';
			$this->bidRedirectionUrl	= isset($responseData['seatbid'][0]['bid'][0]['adm']) ? json_decode($responseData['seatbid'][0]['bid'][0]['adm'])->emailClick->url : '';
		}

		return json_decode(json_encode($responseData));
	}

	public function initiatePlacingBid($responseData){
		return $this->handleResponse($responseData);
	}

	
	private function handleResponse($responseData) {
		$this->return = [
			'code' => 200,
			'message' => '',
			'url' => '',
			'time' => '0',
			'request_data' => $this->data,
			'response_data' => '',
			'source' => 'ExoClick - '.$this->geoISO
		];
		
		if($this->bidRequestCode == 200){
			$bidConfirmation = $this->confirmBid($responseData);
			if($bidConfirmation){
				$this->return['message']  = 'Bid Placed Successfully';
				$this->return['code']     = $this->bidPlacementCode;
				$this->sendPostback($responseData);
			}else{
				$this->return['message']  = 'Bid available but placement is unsuccessful';
				$this->return['code']     = $this->bidPlacementCode;
			}
		}else{
			$this->return['message']	= 'No bid available with floor bid price';
			$this->return['code']		= $this->bidRequestCode;
		}
		
		return $this->prepareReturnData($responseData);
	}

	
	private function confirmBid($responseData) {

		// Placing Bids if HTTP code is
		if($this->bidRequestCode == 200){
			$copen = curl_init($this->bidPlacementUrl);
			curl_setopt($copen, CURLOPT_SSL_VERIFYHOST, false);
			curl_setopt($copen, CURLOPT_SSL_VERIFYPEER, false);
			curl_setopt($copen, CURLOPT_RETURNTRANSFER, true);
			curl_setopt($copen, CURLOPT_HTTPHEADER, [
				'x-openrtb-version: 2.4',
				'Connection: Keep-Alive',
				'Authorization: Bearer '.$this->site_config->api_key,
				'Content-Type: application/json'
			]);
			curl_exec($copen);
			$this->bidPlacementCode = curl_getinfo($copen, CURLINFO_RESPONSE_CODE);
			curl_close($copen);

			// Check and see if bid placement is successful
			if($this->bidPlacementCode == 200){
				// ADM Trigger click URL
				$this->return['url'] = $this->bidRedirectionUrl;
				return true;
			}

			return false;
		}
	}
	
	
	private function sendPostback($responseData) {
		$postbackUrl = "https://trakle02.online/postback?id=" . urlencode($this->params->click_id) . 
               "&payout=" . urlencode($responseData->seatbid[0]->bid[0]->price) . 
               "&currency=" . urlencode($responseData->cur) . 
               "&src=" . urlencode("exoclick").
			   "&type=" . urlencode("rtb").
               "&country_code=" . urlencode($responseData->country_code);

		$postbackCurl = curl_init();
		curl_setopt($postbackCurl, CURLOPT_URL, $postbackUrl);
		curl_setopt($postbackCurl, CURLOPT_SSL_VERIFYHOST, false);
		curl_setopt($postbackCurl, CURLOPT_SSL_VERIFYPEER, false);
		curl_setopt($postbackCurl, CURLOPT_RETURNTRANSFER, true);
		curl_exec($postbackCurl);
		curl_close($postbackCurl);
	}

	
	private function prepareReturnData($responseData) {

		$this->execStats	= EXEC_TIME->stats();
		$this->return['key'] 				= $this->params->key;
		$this->return['response_data'] 		= $responseData;
		$this->return['start_time'] 		= $this->execStats->start;
		$this->return['end_time'] 			= $this->execStats->end;
		$this->return['time'] 				= $this->execStats->duration;
		$this->return['endpoint'] 			= $this->site_config->endpoint;
		$this->return['click_id']         	= $this->params->click_id;
		$this->return['source_id']        	= $this->params->source_id;
		$this->return['is_sellable']      	= $this->params->is_sellable;
		$this->return['email']            	= $this->params->email;
		$this->return['username']         	= $this->params->username;
		$this->return['password']         	= $this->params->password;
		$this->return['gender']           	= $this->params->gender;
		$this->return['looking']          	= $this->params->seek;
		$this->return['dob']              	= $this->params->birth_year .'-'.$this->params->birth_month .'-'.$this->params->birth_day;
		$this->return['device']           	= $this->params->device;
		$this->return['city']             	= $this->params->location;
		$this->return['lander']           	= $this->params->lander;
		$this->return['http']             	= $this->params->http;
		$this->return['antifraud']			= isset($this->params->antifraud) ? json_encode($this->params->antifraud) : '';
		
		// Send Data to Log API
		$log    = new Log($this->return);
		$send   = $log->send($this->config->log->endpoint);
		
		return json_encode($this->return);
	}


}


define('ExoClick', new ExoClick);