<?php
include_once('./log.php');

$config = file_exists('./config/api.json') ? json_decode(file_get_contents('./config/api.json')) : null;

$startTime = microtime(true);


/**
 * Make sure the configuration is not missing or empty
 */
if($config == null){
	$fileMissing = json_encode([
		'code' => 401,
		'message' => 'Api configuration file missing',
	]);
	die($fileMissing);
}

$offer = $_GET['offer'];
$md5Hash = $_GET['traf_id'] ? md5($_GET['traf_id']) : "";
$traf_id = $md5Hash ? substr($md5Hash, 0, 16) : ""; 

$data = [
    'offerId' => $config->rebll->offers->$offer,
    'account' => [
        'username'		=> $_GET['username'],
        'password'		=> $_GET['password'],
        'emailAddress'	=> $_GET['email'],
        'gender'		=> $_GET['gender'],
        'dateOfBirth'	=> $_GET["dob"]
    ],
    'tracking' => [
        's1' => $traf_id,
        's2' => $_GET['click_id'],
        's3' => $_GET['t1_t2'],
        's4' => 'mys4'
    ]
];


$ch = curl_init($config->rebll->endpoint);
curl_setopt_array($ch, array(
	CURLOPT_POST	=> true,
	CURLOPT_RETURNTRANSFER => true,
	CURLOPT_FOLLOWLOCATION => true,
	CURLOPT_HTTPHEADER => array(
        'Authorization: '.$config->rebll->authorization,
		'Content-Type: application/json'
	),
	CURLOPT_POSTFIELDS => json_encode($data)
));

$response = curl_exec($ch);
$responseData = json_decode($response, true);
$httpcode = curl_getinfo($ch, CURLINFO_RESPONSE_CODE);
curl_close($ch);


// Do not include Email in data storage
$data['account']['emailAddress'] = '************';

$return = [
	'code' => 200,
	'message' => '',
	'url' => '',
	'time' => '0',
	'request_data' => $data,
	'response_data' => '',
	'source' => 'Rebll - '.$offer
];

/**
 * 1. Check if user created successfully - build success response
 * 2. If user creation failed - build error response
 */
if($response != false){
	if(is_array($responseData)){

		if($httpcode == 412){
			$return['message']  = $responseData['errors'][0]['detail'];
			$return['code']     = $httpcode;
		}else if($httpcode != 200){
			$return['message']  = $responseData['message'];
			$return['code']     = $httpcode;
		}else{
			$return['message']  = 'Account successfully created';
			$return['url']      = $responseData['successUrl'];
			$return['code']     = 200;
		}

	}
}else {
	$responseData = 'Unable to connect API';
}


$endTime = microtime(true);
$return['key']				= $_GET['key'];
$return['response_data']	= $responseData;
$return['start_time']		= $startTime;
$return['end_time']			= $endTime;
$return['endpoint']			= $config->rebll->endpoint;
$return['time']				= number_format( ( $endTime - $startTime ), 2);
$return['click_id']			= $_GET['click_id'];
$return['source_id']		= $_GET['source_id'];
$return['is_sellable']		= $_GET['is_sellable'];

$return['email']			= $_GET['email'];
$return['username']			= $_GET['username'];
$return['password']			= $_GET['password'];
$return['gender']			= $_GET['gender'];
$return['looking']			= $_GET['looking'];
$return['dob']				= $_GET['dob'];

$return['device']			= $_GET['device'];
$return['city']				= $_GET['city'];
$return['lander']			= $_GET['lander'];
$return['http']				= $_GET['http'];
$return['index_id']			= $_GET['index_id'];
$return['antifraud']		= isset($_GET['antifraud']) ? json_encode($_GET['antifraud']) : '';

// Send Data to Log API
$log	= new Log($return);
$send	= $log->send($config->log->endpoint);

echo json_encode($return);