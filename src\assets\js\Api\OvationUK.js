import axios from 'axios'
import endpoints from '../config/endpoints.json'
import params from '../helper/urlParameters.js'

const ovation = async (formData) => {

    const httpData = {
        email       : formData.email,
        gender      : formData.gender,
        username    : formData.username,
        password    : formData.password,
        dob         : formData.birth_year+'-'+formData.birth_month+'-'+formData.birth_day,
        click_id    : formData.click_id,
        key         : formData.key,
        source_id   : formData.source_id,
        is_sellable : !formData.is_sellable,
        device      : formData.device,
        lander      : formData.lander,
        http        : formData.http,
        offer       : 'uk',
        s4          : formData.s3,
        looking     : formData.seek,
        city        : formData.location,
        index_id    : "222",
        traf_id     : formData.traf_id,
        vars        : formData.t1 + "_" + formData.t2,
        antifraud   : formData.antifraud,
    }

    return await axios.get(endpoints.ovation, { params: httpData })
}

export default ovation