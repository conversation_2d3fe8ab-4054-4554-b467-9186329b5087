<script setup>
import { ref, defineProps, inject } from "vue";
import Steps from "../../lp9/components/views/StepsMarker.vue";
import Language from "../../../assets/js/helper/Language.js";
import Swal from "sweetalert2";
import Searching from "../../../sites/lp12/components//views/Searching.vue";
import config from "../../../assets/js/config/config.json";

const props = defineProps(["steps", "moveNextSlide", "moveBackSlide", "language", "location", "inputs"]);
const steps = ref(props.steps);
const isInputFocused = ref(false);
const assets = inject("assets");

const urlParams = new URLSearchParams(window.location.search);
const isUsernameZero = urlParams.get("username") === "all";

const continueToNextSlide = () => {
  const usernameRegex = /^(?=.*[0-9])(?=.*[a-zA-Z]).{6,14}$/;
  const usernameSimpleRegex = /^.{6,14}$/;
  const alertMessage = document.getElementById("alert-message").innerText;

  if (isUsernameZero) {
    if (!usernameSimpleRegex.test(props.inputs.username.value)) {
      Swal.fire({
        text: "Your username should be between 6 and 14 characters.",
        customClass: {
          popup: "custom-swal-popup",
        },
      });
    } else {
      props.moveNextSlide();
    }
  } else {
    if (!usernameRegex.test(props.inputs.username.value)) {
      Swal.fire({
        text: alertMessage,
        customClass: {
          popup: "custom-swal-popup",
        },
      });
    } else {
      props.moveNextSlide();
    }
  }
};

const onFocus = () => {
  isInputFocused.value = true;
};

const onBlur = () => {
  isInputFocused.value = false;
};

const handleKeyPress = (event) => {
  if (event.key === "Enter") {
    continueToNextSlide();
  }
};

const cdnBase = config.cdn;
const imgPath = config.images.img;
const cardPhotoUrl = `${cdnBase}${imgPath}126.webp`;
</script>

<template>
  <div class="container insta-container">
    <div class="insta-card">
      <div class="insta-header">
        <img class="insta-avatar" :src="assets + 'sexymeetups.png'" alt="Profile" />
      </div>
      <div class="insta-photo">
        <img class="insta-photo-img" :src="cardPhotoUrl" alt="Instagram Photo" />
      </div>
      <div class="insta-content">
        <form>
          <div class="form-group">
            <label for="gender" class="w-100 text-center fs-5 insta-label">{{ Language.username_input_text }}</label>
            <hr />
            <div class="d-flex justify-content-around my-3">
              <div class="d-flex justify-content-center flex-column gap-3 w-100">
                <input v-model="props.inputs.username.value" class="form-control rounded-5 px-5" placeholder="e.g. Alex123" type="text" @focus="onFocus" @blur="onBlur" @keypress="handleKeyPress" />
                <p class="text-center" style="font-size: 13px" v-if="isUsernameZero">{{ Language.username_error_3 }}</p>
                <p class="text-center" style="font-size: 13px" v-else>{{ Language.username_error_2 }}</p>
                <button type="button" class="btn insta-btn w-100" for="option2" @click="continueToNextSlide">{{ Language.continue }}</button>
              </div>
            </div>
          </div>
        </form>
        <div v-if="isUsernameZero" id="alert-message" style="display: none">
          {{ Language.username_error_3 }}
        </div>
        <div v-else id="alert-message" style="display: none">
          {{ Language.username_error_2 }}
        </div>
        <Steps :step="steps" />
        <div class="disclaimer mt-4">
          <div class="progress mb-3 insta-progress" role="progressbar" aria-label="Info example" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100">
            <div class="progress-bar insta-bar" style="width: 56%"></div>
          </div>
          <div class="d-flex justify-content-center">
            <Searching :location="location" :language="Language" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.insta-container {
  min-height: 85vh;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  background: transparent;
  padding-top: 1rem;
}

.insta-card {
  background: #fff;
  border-radius: 1.2em;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.12);
  width: 340px;
  /* width: 100%; */
  overflow: hidden;
  padding-bottom: 0.2em;
}

.insta-header {
  display: flex;
  align-items: center;
  padding: 1em 1em 0.5em 1em;
  background: #fff;
}
.insta-avatar {
  width: 150px;
  height: auto;
  object-fit: cover;
  margin-right: 0.7em;
}
.insta-username {
  font-weight: 600;
  color: #262626;
  font-size: 1.1em;
}

.insta-photo {
  width: 100%;
  aspect-ratio: 1 / 0.8;
  background: #fafafa;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}
.insta-photo-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.insta-content {
  padding: 0.8em 1em 0.3em 1em;
}
.insta-label {
  font-weight: 500;
  color: #262626;
  font-size: 1.1em;
}
.insta-btn {
  width: 7em;
  height: 2.7em;
  border: none;
  border-radius: 1.2em;
  background: linear-gradient(90deg, #e1306c 0%, #fdc468 100%);
  color: #fff;
  font-weight: 600;
  font-size: 1em;
  box-shadow: 0 2px 8px rgba(225, 48, 108, 0.1);
  transition: background 0.2s, color 0.2s, transform 0.1s;
}
.insta-btn:hover {
  background: linear-gradient(90deg, #fdc468 0%, #e1306c 100%);
  color: #fff;
  transform: translateY(-2px) scale(1.04);
}
.insta-progress {
  background: #f7f7f7;
  border-radius: 1em;
}
.insta-bar {
  background: linear-gradient(90deg, #e1306c 0%, #fdc468 100%) !important;
}
.disclaimer {
  text-align: center;
  margin-top: 1em;
}
@media (max-width: 500px) {
  .insta-container {
    padding-top: 1rem;
    min-height: 100vh;
  }
  .insta-card {
    max-width: 98vw;
    border-radius: 0.7em;
  }
  .insta-content {
    padding: 0.7em 0.3em 0.3em 0.3em;
  }
  .insta-header {
    padding: 0.7em 0.5em 0.5em 0.5em;
  }
}
</style>
