<script setup>
import { ref, defineProps } from "vue";
import Steps from "../../lp9/components/views/StepsMarker.vue";
import Language from "../../../assets/js/helper/Language.js";
import Swal from "sweetalert2";
import Searching from "../../../components/Searching.vue";

const props = defineProps(["steps", "moveNextSlide", "moveBackSlide", "language", "location", "inputs"]);
const steps = ref(props.steps);
const isInputFocused = ref(false);

const urlParams = new URLSearchParams(window.location.search);
const isUsernameZero = urlParams.get("username") === "all";

const continueToNextSlide = () => {
  const usernameRegex = /^(?=.*[0-9])(?=.*[a-zA-Z]).{6,14}$/;
  const usernameSimpleRegex = /^.{6,14}$/;
  const alertMessage = document.getElementById("alert-message").innerText;

  if (isUsernameZero) {
    if (!usernameSimpleRegex.test(props.inputs.username.value)) {
      Swal.fire({
        text: "Your username should be between 6 and 14 characters.",
        customClass: {
          popup: "custom-swal-popup",
        },
      });
    } else {
      props.moveNextSlide();
    }
  } else {
    if (!usernameRegex.test(props.inputs.username.value)) {
      Swal.fire({
        text: alertMessage,
        customClass: {
          popup: "custom-swal-popup",
        },
      });
    } else {
      props.moveNextSlide();
    }
  }
};

const onFocus = () => {
  isInputFocused.value = true;
};

const onBlur = () => {
  isInputFocused.value = false;
};

const handleKeyPress = (event) => {
  if (event.key === "Enter") {
    continueToNextSlide();
  }
};
</script>

<template>
  <div class="container-fluid d-flex align-items-center justify-content-center min-vh-100">
    <div class="text-center">
      <p class="fs-1 mb-5">{{ Language.username_input_text }}</p>
      <div class="d-flex justify-content-center gap-5 mb-5 button-container">
        <input v-model="props.inputs.username.value" class="form-control rounded-5" style="width: 25em" placeholder="e.g. Alex123" type="text" @focus="onFocus" @blur="onBlur" @keypress="handleKeyPress" />
        <button type="button" class="btn btn-danger rounded-5 fs-5" style="height: 5em; width: 21em" @click="continueToNextSlide">{{ Language.continue }}</button>
      </div>
      <div class="steps d-flex gap-3 justify-content-center mb-3">
        <i class="fa-solid fa-star fs-3 text-white"></i>
        <i class="fa-solid fa-star fs-3 text-white"></i>
        <i class="fa-solid fa-star fs-3 text-white"></i>
        <i class="fa-solid fa-star fs-3 text-white"></i>
        <i class="fa-regular fa-star fs-3 text-white"></i>
        <i class="fa-regular fa-star fs-3 text-white"></i>
        <i class="fa-regular fa-star fs-3 text-white"></i>
      </div>
      <Searching :location="location" :language="Language" />
    </div>
    <div v-if="isUsernameZero" id="alert-message" style="display: none">
      {{ Language.username_error_3 }}
    </div>
    <div v-else id="alert-message" style="display: none">
      {{ Language.username_error_2 }}
    </div>
    <Steps :step="steps" />
  </div>
</template>

<style scoped>
@media (max-width: 768px) {
  .fs-1 {
    font-size: 1.5rem;
  }
  .button-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
  .form-control {
    width: 20em !important;
    height: 4em;
  }
  .btn {
    width: 16em !important;
    height: 3em !important;
  }
  .steps {
    margin-top: 1em;
  }
}
</style>
