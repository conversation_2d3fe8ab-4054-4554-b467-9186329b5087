<script setup>
import { ref } from "vue";

const props = defineProps(["language"]);

const timer = ref(0);
let seconds = 60;
let counter = 1;

setInterval(function () {
  seconds == 0 ? (seconds = 60) : (seconds -= 1);
  seconds == 0 && counter > 0 ? counter-- : "";
  counter == 0 ? (counter = 1.0) : "";
  timer.value = "0" + counter + ":" + (seconds.toString().length == 1 ? (seconds = "0" + seconds) : seconds);
}, 1000);
</script>

<template>
  <div class="d-flex align-items-end flex-column">
    <div class="p-2 fs-6 text-white stroke">{{ language.offer_text }}</div>
    <div class="p-2 align-self-end fs-6">
      <span class="border border-white border-4 px-4 py-2 text-white stroke">{{ timer }}</span>
    </div>
  </div>
</template>
