<script setup>
defineProps(['step'])
</script>

<template>
    <div class="d-flex justify-content-center mt-3">
        <div class="form-check">
            <input class="form-check-input custom-radio" type="radio" value="option1" :checked="step >= 1" disabled>
        </div>
        <div class="form-check">
            <input class="form-check-input custom-radio" type="radio" value="option2" :checked="step >= 2" disabled>
        </div>
        <div class="form-check">
            <input class="form-check-input custom-radio" type="radio" value="option2" :checked="step >= 3" disabled>
        </div>
        <div class="form-check">
            <input class="form-check-input custom-radio" type="radio" value="option2" :checked="step >= 4" disabled>
        </div>
        <div class="form-check">
            <input class="form-check-input custom-radio" type="radio" value="option2" :checked="step >= 5" disabled>
        </div>
    </div>
</template>


<style scoped>
    .custom-radio{
        background: #fefefe;
        opacity: 10 !important;
        font-size: 12px;
        border: solid 1px #fefefe;
    }.custom-radio:checked[type=radio]{
        background: #FF8C00;
        opacity: 1;
        border: solid 4px #FF8C00;
        border: solid 1px #fefefe;
    }
</style>