<?php
include_once('./log.php');

$config = file_exists('./config/api.json') ? json_decode(file_get_contents('./config/api.json')) : null;

/**
 * Make sure the configuration is not missing or empty
 */
if($config == null){
	$fileMissing = json_encode([
		'code' => 401,
		'message' => 'Api configuration file missing',
	]);
	die($fileMissing);
}

$offer = $_GET['offer'];

$startTime = microtime(true);

$md5Hash = $_GET['traf_id'] ? md5($_GET['traf_id']) : "";
$traf_id = $md5Hash ? substr($md5Hash, 0, 16) : "";
$source = $traf_id;

$data = [
	"ip" 			=> $_SERVER['REMOTE_ADDR'],
	"userAgent"		=> $_GET['device']['raw'],
	"email"			=> $_GET['email'],
	"countryCode"	=> strtoupper( $_GET['offer'] ),
	"username"		=> $_GET['username'],
	"password"		=> $_GET['password'],
	"deviceType"	=> $_GET['media'],
	"gender"		=> strtoupper($_GET['gender']),
	"searchGender"	=> strtoupper($_GET['looking']),
	"birthdate"		=> $_GET['birth_year'] . '-' . $_GET['birth_month'] . '-' . $_GET['birth_day'],
	"cid"			=> $_GET['click_id'],
	"source"		=> $source,
	'subsource'		=> $_GET['t1']
];

// Replace all placeholder of endpoints
$endpoint	= $config->wiimax->$offer->endpoint. '&subsource='. $_GET['t1'];
$endpoint	= str_replace([ '{cid}', '{source}' ], [ $_GET['click_id'], $traf_id ], $endpoint);


$ch = curl_init($endpoint);
curl_setopt_array($ch, array(
	CURLOPT_POST	=> true,
	CURLOPT_RETURNTRANSFER => true,
	CURLOPT_FOLLOWLOCATION => true,
	CURLOPT_HTTPHEADER => array(
		'Content-Type: application/json',
		'Content-Type: ' . $config->wiimax->$offer->content_type,
		'Authorization: ' . $config->wiimax->$offer->authorization
	),
	CURLOPT_POSTFIELDS => json_encode($data)
));

$response = curl_exec($ch);
$responseData = json_decode($response, true);
$httpcode = curl_getinfo($ch, CURLINFO_RESPONSE_CODE);
curl_close($ch);

// Do not include Email in data storage
$data['email'] = '************';

$return = [
	'code' => 200,
	'message' => '',
	'url' => '',
	'time' => '0',
	'request_data' => $data,
	'response_data' => '',
	'source' => 'Wiimax ' . $offer
];

/**
 * 1. Check if user created successfully - build success response
 * 2. If user creation failed - build error response
 */
if($response != false){
	if(is_array($responseData)){
		if($responseData['success'] == false){
			foreach($responseData['errors'] as $error){
				$return['message'] = $error;
				$return['code'] = 403;
			}
		}else{
			
			$return['message'] = 'Account created successfully';
			$return['code'] = $httpcode;
			$return['url']	= $responseData['loginUrl'];
		}
	}
}else {
	$responseData = 'Unable to connect API';
}

$endTime = microtime(true);
$return['key']				= $_GET['key'];
$return['response_data']	= $responseData ? $responseData : $response;
$return['start_time']		= $startTime;
$return['end_time']			= $endTime;
$return['endpoint']			= $endpoint;
$return['time'] 			= number_format( ( $endTime - $startTime ), 2);
$return['click_id']			= $_GET['click_id'];
$return['source_id']		= $_GET['source_id'];
$return['is_sellable']		= $_GET['is_sellable'];

$return['email']			= $_GET['email'];
$return['username']			= $_GET['username'];
$return['password']			= $_GET['password'];
$return['gender']			= $_GET['gender'];
$return['looking']			= $_GET['looking'];
$return['dob']				= $_GET['birth_year'] . '-' . $_GET['birth_month'] . '-' . $_GET['birth_day'];

$return['device']			= $_GET['device'];
$return['city']				= $_GET['city'];
$return['lander']			= $_GET['lander'];
$return['http']				= $_GET['http'];
$return['index_id']			= $_GET['index_id'];
$return['antifraud']		= isset($_GET['antifraud']) ? json_encode($_GET['antifraud']) : '';


// Send Data to Log API
$log	= new Log($return);
$send	= $log->send($config->log->endpoint);

echo json_encode($return);