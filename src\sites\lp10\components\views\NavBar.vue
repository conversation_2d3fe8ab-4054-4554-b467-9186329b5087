<template>
  <div class="lp2nav mt-2">
    <nav class="d-flex justify-content-between px-3">
      <div class="icon">
        <img src="../../../../assets/logo2.png" style="height:2.5em;" alt="">
      </div>
      <div class="timer">
        <div id="timerDisplay" class="text-warning fw-bold">Your Offer will expire in:
          <div class="d-flex justify-content-end">
            <span class="text-danger fw-bold fs-4">{{ minutes }}:</span>
            <span class="text-danger fw-bold fs-4">{{ seconds }}</span>
          </div>
        </div>
      </div>
    </nav>
  </div>
</template>

<script>
export default {
  name: "NavBar",
  data() {
    return {
      totalSeconds: 120,
      minutes: 2,
      seconds: 0,
      intervalId: null,
    };
  },
  methods: {
    updateTimerDisplay() {
      this.minutes = Math.floor(this.totalSeconds / 60);
      this.seconds = this.totalSeconds % 60;
    },
    timerCallback() {
      if (this.totalSeconds > 0) {
        this.totalSeconds--;
        this.updateTimerDisplay();
      } else {
        this.totalSeconds = 120;
        this.updateTimerDisplay();
      }
    },
  },
  mounted() {
    this.updateTimerDisplay();
    this.intervalId = setInterval(this.timerCallback, 1000);
  },
  beforeUnmount() {
    clearInterval(this.intervalId);
  },
};
</script>

