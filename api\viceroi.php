<?php
include_once('./log.php');

$config = file_exists('./config/api.json') ? json_decode(file_get_contents('./config/api.json')) : null;

$startTime = microtime(true);

/**
 * Make sure the configuration is not missing or empty
 */
if($config == null){
	$fileMissing = json_encode([
		'code' => 401,
		'message' => 'Api configuration file missing',
	]);
	die($fileMissing);
}

$offer = $_GET['offer'];

$endpoint = $config->viceroi->$offer->url;

$data = [
	'email' 		=> $_GET['email'],
	'ip_address'	=> $_GET['device']['ip'],
	'pid'			=> '1502',
	'click_id'		=> $_GET['click_id'],
	'sub1'			=> $_GET['subid'],
	'user_agent'	=> $_GET['device']['raw'],
	'password'		=> $_GET['password'],
	'gender'		=> $_GET['gender'],
	'date_of_birth'	=> $_GET['dob']
];

$ch = curl_init($endpoint);
curl_setopt_array($ch, array(
	CURLOPT_POST	=> true,
	CURLOPT_RETURNTRANSFER => true,
	CURLOPT_FOLLOWLOCATION => true,
	CURLOPT_HTTPHEADER => array(
		'Content-Type: application/json'
	),
	CURLOPT_POSTFIELDS => json_encode($data)
));

$response = curl_exec($ch);
$responseData = json_decode($response, true);
$httpcode = curl_getinfo($ch, CURLINFO_RESPONSE_CODE);
curl_close($ch);


// Do not include Email in data storage
$data['email'] = '************';

$return = [
	'code' => 200,
	'message' => '',
	'url' => '',
	'time' => '0',
	'request_data' => $data,
	'response_data' => '',
	'source' => 'viceroi '.$offer
];

/**
 * 1. Check if user created successfully - build success response
 * 2. If user creation failed - build error response
 */

if($response != false){
	if(is_array($responseData)){
		if($httpcode == 200 || $httpcode == 201 || $responseData['message'] == 'Successful'){
			$return['message'] = $responseData['message'];
			$return['code'] = 200;
			$return['url']	= $responseData['redirect_url'];
		}else{
			$return['message'] = $responseData['status'];
			$return['code'] = $httpcode;
		}
	}
}else{
	$responseData = 'Unable to connect API';
}

$endTime = microtime(true);
$return['key']				= $_GET['key'];
$return['response_data']	= $responseData;
$return['start_time']		= $startTime;
$return['end_time']			= $endTime;
$return['endpoint']			= $endpoint;
$return['time']				= number_format( ( $endTime - $startTime ), 2);
$return['click_id']			= $_GET['click_id'];
$return['source_id']		= $_GET['subid'];
$return['is_sellable']		= $_GET['is_sellable'];

$return['email']			= $_GET['email'];
$return['username']			= $_GET['username'];
$return['password']			= $_GET['password'];
$return['gender']			= $_GET['gender'];
$return['looking']			= $_GET['looking'];
$return['dob']				= $_GET['dob'];

$return['device']			= $_GET['device'];
$return['city']				= $_GET['city'];
$return['lander']			= $_GET['lander'];
$return['http']				= $_GET['http'];
$return['index_id']			= $_GET['index_id'];
$return['antifraud']		= isset($_GET['antifraud']) ? json_encode($_GET['antifraud']) : '';

// Send Data to Log API
$log	= new Log($return);
$send	= $log->send($config->log->endpoint);

echo json_encode($return);