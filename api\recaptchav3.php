<?php

$url    = "https://www.google.com/recaptcha/api/siteverify";

$data   = array(
    'secret'    => "6LcVHV4qAAAAAHcAYn8a3TNLB3bv7m87BSwiHAeM",
    "response"  => $_GET['token']
);

$postJSON = http_build_query($data);

$curl = curl_init();
curl_setopt($curl, CURLOPT_URL, "https://www.google.com/recaptcha/api/siteverify");
curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
curl_setopt($curl, CURLOPT_POST, 1);
curl_setopt($curl, CURLOPT_POSTFIELDS, $postJSON);
$response = curl_exec($curl);
curl_close($curl);


echo $response;