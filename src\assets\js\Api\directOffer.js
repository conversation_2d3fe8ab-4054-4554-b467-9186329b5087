import axios from "axios";
import endpoints from "../config/endpoints.json";
import params from "../helper/urlParameters.js";
import device from "../helper/deviceDetection.js";

const directoffer = async (formData) => {
  const httpData = {
    username: formData.username,
    password: formData.password,
    email: formData.email,
    gender: formData.gender,
    seek: formData.seek,
    location: formData.location,
    birth_day: formData.birth_day,
    birth_month: formData.birth_month,
    birth_year: formData.birth_year,
    media: device().device,
    sub_id: formData.click_id, // Unique identifier, e.g. Click ID,
    req_id: params().offer, // Unsure what is this, need to ask to team
    aff_id: formData.cmp, // Source ID
    key: formData.key, // Unique Identifier for Logger
    is_sellable: !formData.is_sellable,
    lander: formData.lander,
    device: formData.device,
    http: formData.http,
    city: formData.location,
    index_id: "269",
    traf_id: formData.traf_id,
    antifraud: formData.antifraud,
    tsid: formData.tsid,
  };

  return await axios.get(endpoints.directoffer, { params: httpData });
};

export default directoffer;
