<script setup>
import { ref, defineProps } from "vue";
import Api from "../../../assets/js/helper/api.js";
import Language from "../../../assets/js/helper/Language.js";
import Assets from "../../../assets/js/config/config.json";
import Params from "../../../assets/js/helper/urlParameters";

/// Define component props
const props = defineProps(["inputs"]);
const apiErrorMessage = ref(false);
const disableSubmitBtn = ref(false);
const video = ref(undefined);

const cdn = Assets.cdn;
// Query Card Image
const cardImage = "image" in Params().raw ? Assets.images.img + Params().raw["image"] + ".webp" : Assets.images.img + "1.webp";

let counter = 33;
let fade = 0;

function updateProgress() {
  const progressBarDivs = document.querySelectorAll(".progress div");
  const loadingTexts = document.querySelectorAll(".loading-text");

  progressBarDivs.forEach((div) => {
    div.style.width = `${counter}%`;
  });

  fade++;
  document.querySelectorAll(`.list-fade[fade='${fade}']`).forEach((element) => {
    element.classList.remove("d-none");
  });
  if (counter > 100) {
    clearInterval(loader);
    document.querySelector(".progress").style.display = "none";
    loadingTexts.forEach((text) => {
      text.classList.remove("d-block");
      text.style.display = "none";
    });
    setTimeout(() => {
      let formData = {};
      for (let x in props.inputs) {
        formData[x] = props.inputs[x]["value"];
      }
      Api(formData, disableSubmitBtn, apiErrorMessage);
    }, 1);
  }

  counter += 33;
}

const loader = setInterval(updateProgress, 2500);
</script>

<template>
  <div class="container-fluid">
    <div class="card card-lp2 transparent-bg lp2" style="width: 24rem; height: 40rem">
      <img :src="cdn + cardImage" class="card-img" v-if="video == undefined" />
      <div class="card-body d-flex flex-column" :class="video ? '' : 'card-img-overlay'">
        <div class="animate words mt-auto">
          <span id="searching-text" class="fw-light text-center d-block fs-5 mb-2 lh-sm">{{ Language.after_questionaire_title }}</span>
          <span id="congratulations-text" class="fw-bold congrats text-center mb-2 text-success d-block fs-3 list-fade">{{ Language.blue_text_02 }}</span>
          <span id="congratulations-text" class="fw-light text-center mb-2 text-success d-block fs-6 list-fade">{{ Language.blue_text_03 }}</span>
          <div id="progress-bar-container" class="progress" style="height: 15px">
            <div id="progress-bar" class="progress-bar progress-bar-striped progress-bar-animated bg-info" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
          </div>
          <ul class="list-group col-md-12 mt-3 mx-auto mb-4">
            <li class="list-group-item d-flex justify-content-between align-items-center border-0 px-4 d-none list-fade lss text-white" fade="1">
              <span class="text-dark">{{ Language.validation_check_01 }}</span>
              <span>
                <svg xmlns="http://www.w3.org/2000/svg" width="26" height="26" fill="currentColor" class="bi bi-check-lg text-success" viewBox="0 0 16 16">
                  <path d="M12.736 3.97a.733.733 0 0 1 1.047 0c.286.289.29.756.01 1.05L7.88 12.01a.733.733 0 0 1-1.065.02L3.217 8.384a.757.757 0 0 1 0-1.06.733.733 0 0 1 1.047 0l3.052 3.093 5.4-6.425a.247.247 0 0 1 .02-.022Z" />
                </svg>
              </span>
            </li>
            <li class="list-group-item d-flex justify-content-between align-items-center border-0 px-4 d-none list-fade lss text-white" fade="2">
              <span class="text-dark">{{ Language.validation_check_02 }}</span>
              <span>
                <svg xmlns="http://www.w3.org/2000/svg" width="26" height="26" fill="currentColor" class="bi bi-check-lg text-success" viewBox="0 0 16 16">
                  <path d="M12.736 3.97a.733.733 0 0 1 1.047 0c.286.289.29.756.01 1.05L7.88 12.01a.733.733 0 0 1-1.065.02L3.217 8.384a.757.757 0 0 1 0-1.06.733.733 0 0 1 1.047 0l3.052 3.093 5.4-6.425a.247.247 0 0 1 .02-.022Z" />
                </svg>
              </span>
            </li>
            <li class="list-group-item d-flex justify-content-between align-items-center border-0 px-4 d-none list-fade lss text-white" fade="3">
              <span class="text-dark">{{ Language.validation_check_03 }}</span>
              <span>
                <svg xmlns="http://www.w3.org/2000/svg" width="26" height="26" fill="currentColor" class="bi bi-check-lg text-success" viewBox="0 0 16 16">
                  <path d="M12.736 3.97a.733.733 0 0 1 1.047 0c.286.289.29.756.01 1.05L7.88 12.01a.733.733 0 0 1-1.065.02L3.217 8.384a.757.757 0 0 1 0-1.06.733.733 0 0 1 1.047 0l3.052 3.093 5.4-6.425a.247.247 0 0 1 .02-.022Z" />
                </svg>
              </span>
            </li>
          </ul>
          <div class="d-flex justify-content-center">
            <p class="me-3 fw-bold fs-6">{{ Language.almost_there }}</p>
            <div id="spinner" class="spinner-border text-success" role="status"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style>
.smtc {
  font-size: 11px !important;
}

.usa {
  font-size: 9px !important;
}

.password {
  font-size: 13px !important;
}

.agree {
  font-size: 15px !important;
}

.bg-info {
  background-color: rgb(237, 24, 126) !important;
}

.lss {
  background-color: #973395 !important;
}

.congrats {
  color: rgb(237, 24, 126) !important;
}

.border-0 {
  background-color: transparent !important;
}
</style>
