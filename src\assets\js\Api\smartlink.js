import axios from 'axios'
import endpoints from '../../../assets/js/config/endpoints.json'

const cummission = async (formData) => {

    let httpData = {
        username    : formData.username,
        password    : formData.password,
        gender      : formData.gender,
        email       : formData.email,
        sex         : formData.gender,
        birthdate   : formData.birth_year +'-'+formData.birth_month+'-'+formData.birth_day,
        click_id    : formData.click_id,
        source_id   : formData.source_id,
        key         : formData.key,
        is_sellable : !formData.is_sellable,
        device      : formData.device,
        lander      : formData.lander,
        http        : formData.http,
        l           : formData.l,
        looking     : formData.seek,
        t1_t2       : formData.cmp,
        city        : formData.location,
        traf_id     : formData.traf_id,
        antifraud   : formData.antifraud,
        alreadyRegistered   : formData.alreadyRegistered,
    }
    //Send data to api
    return await axios.get(endpoints.smartlink, { params: httpData} )
}

export default cummission