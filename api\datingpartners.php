<?php
include_once('./log.php');

$config = file_exists('./config/api.json') ? json_decode(file_get_contents('./config/api.json')) : null;

$startTime = microtime(true);

/**
 * Make sure the configuration is not missing or empty
 */
if($config == null){
	$fileMissing = json_encode([
		'code' => 401,
		'message' => 'Api configuration file missing',
	]);
	die($fileMissing);
}


$offer	= $_GET['offer'];
$genderInput = strtolower($_GET['gender']);

if ($genderInput == 'male') {
    $gender = 'm';
} elseif ($genderInput == 'female') {
    $gender = 'f';
}

$age		= (date('Y') - date('Y', strtotime($_GET['dob'])));
$email		= $_GET['email'];
$username	= $_GET['username']; 

$data = [
	'email'		=> $email,
	'name'		=> $username,
	'gender'	=> $gender,
	'age'		=> $age
];

$dataToSend = "{
	'email': {$email},
	'name':  {$username},
	'gender': {$gender},
	'age': {$age}
}";

$return = [
	'code' => 200,
	'message' => '',
	'url' => '',
	'time' => '0',
	'request_data' => json_encode($data),
	'response_data' => '',
	'source' => 'DatingPartners '.$offer
];



// check if the email is successful
$emailValidationUrl = str_replace('{user_email}', $_GET['email'], $config->datingpartners->email_validation);
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $emailValidationUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
if (curl_errno($ch)) {
	echo 'cURL error: ' . curl_error($ch);
}

curl_close($ch);
$response = json_decode($response, true);
$offer = $_GET['offer'];

if($httpCode == 200 || $httpCode == 201){
	if($response['result']['status'] == 0){
		$sub4 = base64_encode($dataToSend);

		$md5Hash = $_GET['traf_id'] ? md5($_GET['traf_id']) : "";
		$traf_id = $md5Hash ? substr($md5Hash, 0, 16) : ""; 

		$traf_id = $traf_id ? $traf_id . "_" : "";
		$affiliate_id = $traf_id . $_GET['t1'];
		$affiliate_source = $traf_id . $_GET['t2'];

		$replacements = [
			'{udata}' => $sub4,
			'{clickid}' => $_GET['click_id'],
			'{affiliate_id}' => $affiliate_id,
			'{affiliate_source}' => $affiliate_source,
		];
		
		$trackingUrl = strtr($config->datingpartners->$offer->endpoint, $replacements);

		$return['message']			= "Success";
		$return['code']				= 200;
		$return['url']				= $trackingUrl;
	}
	elseif($response['result']['status'] == 2){
		$return['message']			= "Wrong or invalid email";
		$return['code']				= 403;
	}
	elseif($response['result']['status'] == 3){
		$return['message']			= "The user already has a registration on that web-site.";
		$return['code']				= 422;
	}
}


// Do not include Email in data storage
$data['email'] = '************';

$endTime = microtime(true);
$return['key']				= $_GET['key'];
$return['response_data']	= json_encode($response);
$return['start_time']		= $startTime;
$return['end_time']			= $endTime;
$return['endpoint']			= $trackingUrl;
$return['time']				= number_format( ( $endTime - $startTime ), 2);
$return['click_id']			= $_GET['click_id'];
$return['source_id']		= $_GET['subid'];
$return['is_sellable']		= $_GET['is_sellable'];

$return['email']			= $_GET['email'];
$return['username']			= $_GET['username'];
$return['password']			= $_GET['password'];
$return['gender']			= $_GET['gender'];
$return['looking']			= $_GET['looking'];
$return['dob']				= $_GET['dob'];

$return['device']			= $_GET['device'];
$return['city']				= $_GET['city'];
$return['lander']			= $_GET['lander'];
$return['http']				= $_GET['http'];
$return['index_id']			= $_GET['index_id'];
$return['antifraud']		= isset($_GET['antifraud']) ? json_encode($_GET['antifraud']) : '';


// Send Data to Log API
$log	= new Log($return);
$send	= $log->send($config->log->endpoint);

echo json_encode($return);