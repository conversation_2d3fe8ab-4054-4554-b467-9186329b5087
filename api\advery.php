<?php
include_once('./log.php');

$config = file_exists('./config/api.json') ? json_decode(file_get_contents('./config/api.json')) : null;

$startTime = microtime(true);


/**
 * Make sure the configuration is not missing or empty
 */
if($config == null){
    $fileMissing = json_encode([
        'code' => 401,
        'message' => 'Api configuration file missing',
    ]);
    die($fileMissing);
}

$traf_id = $_GET['traf_id'] ? $_GET['traf_id'] : "";
$md5Hash = md5($traf_id);
$traf_id = substr($md5Hash, 0, 16);

$offer = $_GET['offer'] ? $_GET['offer'] : 'us';
// $traf_id = $traf_id ? $traf_id . "_" : "";

$sexual_orientation = $_GET['looking'] != $_GET['gender'] ? 'hetero' : 'homo';

// Format endpoint URL with parameters
$endpoint = $config->advery->$offer->endpoint;
$endpoint = str_replace('{p1}', $traf_id, $endpoint);
$endpoint = str_replace('{s2}', $_GET['t1'], $endpoint);
$endpoint = str_replace('{data2}', $_GET['click_id'], $endpoint);

$data = [
    'apiKey'        => $config->advery->api_key,
    'email'         => $_GET['email'],
    'sexual_orientation' => $sexual_orientation,
    'gender'        => $_GET['gender'],
    'dob'           => $_GET['birthdate'],
    'ip'            => $_GET['device']['ip'],
    'ua'            => $_GET['device']['raw'],
    'utm_campaign'  => $config->advery->utm_campaign,
    'utm_source'    => $config->advery->utm_source,
    'data2'         => $_GET['click_id'],
];

$return = [
    'code' => 200,
    'message' => '',
    'url' => '',
    'time' => '0',
    'source' => 'advery - ' . $offer
];


$ch = curl_init($endpoint);

curl_setopt_array($ch, array(
    CURLOPT_POST    => true,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_FOLLOWLOCATION => true,
    CURLOPT_POSTFIELDS => json_encode($data),
    CURLOPT_HTTPHEADER => array(
        'Content-Type: application/json'
    )
));

$response = curl_exec($ch);
$responseData = json_decode($response, true);
$httpcode = curl_getinfo($ch, CURLINFO_RESPONSE_CODE);
curl_close($ch);

$return = [
    'code' => 200,
    'message' => '',
    'url' => '',
    'time' => '0',
    'request_data' => $data,
    'response_data' => '',
    'source' => 'Advery '.$offer
];

/**
 * 1. Check if user created successfully - build success response
 * 2. If user creation failed - build error response
 */

$response = (array)json_decode($response);

if($response['status'] == 'success'){
    $return['message']  = 'Account successfully created';
    $return['url']      = $response['location'];
    $return['code']     = 200;
}else{
    $return['message']  = $responseData['message'];
    $return['code']     = 400;
}


$endTime = microtime(true);
$return['key']              = $_GET['key'];
$return['response_data']    = $response;
$return['start_time']       = $startTime;
$return['end_time']         = $endTime;
$return['endpoint']         = $endpoint;
$return['time']             = number_format( ( $endTime - $startTime ), 2);
$return['click_id']         = $_GET['click_id'];
$return['source_id']        = $_GET['source_id'];
$return['is_sellable']      = $_GET['is_sellable'];

$return['email']            = $_GET['email'];
$return['username']         = $_GET['username'];
$return['password']         = $_GET['password'];
$return['gender']           = $_GET['gender'];
$return['looking']          = $_GET['looking'];
$return['dob']              = $_GET['birthdate'];

$return['device']           = $_GET['device'];
$return['city']             = $_GET['city'];
$return['lander']           = $_GET['lander'];
$return['http']             = $_GET['http'];
$return['index_id']			= $_GET['index_id'];
$return['antifraud']		= isset($_GET['antifraud']) ? json_encode($_GET['antifraud']) : '';

// Send Data to Log API
$log    = new Log($return);
$send   = $log->send($config->log->endpoint);

echo json_encode($return);
