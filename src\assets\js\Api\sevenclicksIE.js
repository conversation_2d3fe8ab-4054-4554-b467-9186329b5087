import axios from 'axios'
import endpoints from '../config/endpoints.json'
import params from '../helper/urlParameters.js'
import device from '../helper/deviceDetection.js'

const sevenclicksie = async (formData) => {    

    const httpData = {
        username            :formData.username,
        password            :formData.password,
        email               :formData.email,
        gender              :formData.gender,
        looking             :formData.seek,
        location            :formData.location,
        dob                 : formData.birth_year + "-" + formData.birth_month + "-" + formData.birth_day,
        click_id            :formData.click_id, // Unique identifier, e.g. Click ID,
        source_id           :formData.cmp, // Source ID
        key                 :formData.key, // Unique Identifier for Logger
        is_sellable         :!formData.is_sellable,
        lander              :formData.lander,
        device              :formData.device,
        http                :formData.http,
        offer               :"ie",
        city                :formData.location,
        index_id            : "137",
        traf_id             : formData.traf_id,
        antifraud   : formData.antifraud,
    }

    return await axios.get(endpoints.sevenclicks, { params: httpData })
}

export default sevenclicksie