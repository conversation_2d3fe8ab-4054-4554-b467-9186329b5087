<script setup>
import Params from "../assets/js/helper/urlParameters";
import config from "../assets/js/config/config.json";

const image = "image" in Params().raw ? config.cdn + config.images.img + Params().raw.image + ".webp" : false;

const video = "video" in Params().raw ? config.cdn + config.videos.mp4 + Params().raw.video + ".mp4" : false;
</script>

<template>
  <div class="background-image" v-if="image">
    <img :src="image" alt="" class="background-media" />
  </div>
  <div class="background-image" v-if="video">
    <video autoplay loop muted playsinline class="background-media">
      <source :src="video" type="video/mp4" />
    </video>
  </div>
</template>

<style scoped>
.background-image {
  width: 100% !important;
  height: 100% !important;
  position: fixed;
  z-index: -100;
}
.background-media {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover;
  object-position: 50% 50%;
}
</style>
