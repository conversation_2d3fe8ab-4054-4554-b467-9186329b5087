<?php
include_once('./log.php');
include_once('./call.php');

$config = file_exists('./config/api.json') ? json_decode(file_get_contents('./config/api.json')) : null;

/**
 * Make sure the configuration is not missing or empty
 */
if($config == null){
	$fileMissing = json_encode([
		'code' => 401,
		'message' => 'Api configuration file missing',
	]);
	die($fileMissing);
}

$offer = $_GET['offer'] ? $_GET['offer'] : 'us';

$md5Hash = $_GET['traf_id'] ? md5($_GET['traf_id']) : "";
$traf_id = $md5Hash ? substr($md5Hash, 0, 16) : ""; 

$traf_id = $traf_id ? $traf_id . "_" : "";
$sub = $traf_id . $_GET['t1'];

$data = [
	'email'		=> $_GET['email'],
	'username'	=> $_GET['username'],
	'password'	=> $_GET['password'],
    'gender'	=> $_GET['gender'],
	'birthday'  => $_GET['dob'],
    'locale'    => $config->ais->offers->$offer->lan,
    'sub2'		=> $_GET['click_id'],
	'sub1'		=> $sub,
	'offerId'	=> $config->ais->offers->$offer->code,
	'ip'		=> $_GET['device']['ip']
];

$return = [
	'code' => 200,
	'message' => '',
	'url' => '',
	'time' => '0',
	'source' => 'AIS - ' . $offer
];

$startTime	= microtime(true);
$response	= '';

// Login
$login = new Call( $config->ais->login->endpoint, (array) $config->ais->header, (array) $config->ais->login->credentials, true);
$response = $login->responseObject();


// Error Checker
if($login->responseArray() && array_key_exists('code', $login->responseArray())){
	$return['message']	= $response->message;
	$return['code']		= $response->code;
}else{

	$header = (array) $config->ais->header;
	$header[] = "Authorization: Bearer ".$response->token;

	// Check sites available to us
	$data['siteId'] = $config->ais->offers->$offer->sideId;
	
	$send = new Call( $config->ais->register->endpoint, $header, $data, true);
	$response = $send->responseObject();

	if(isset($response->errors)){
		foreach($response->errors as $val){
			$return['message']	= $val->message;
		}
	}else{
		$return['message']	= 'Account successfully created';
		$return['url']		= $response->loginLink;
	}

	$return['code'] = $send->info;

}

// Do not include Email in data storage
$data['email'] = '************';

$endTime = microtime(true);
$return['key']				= $_GET['key'];
$return['request_data']		= $data;
$return['response_data']	= isset($response) ? $response : '';
$return['start_time']		= $startTime;
$return['end_time']			= $endTime;
$return['endpoint']			= $config->ais->register->endpoint;
$return['time']				= number_format( ( $endTime - $startTime ), 2);
$return['click_id']			= $_GET['click_id'];
$return['source_id']		= $_GET['source_id'];
$return['is_sellable']		= $_GET['is_sellable'];

$return['email']			= $_GET['email'];
$return['username']			= $_GET['username'];
$return['password']			= $_GET['password'];
$return['gender']			= $_GET['gender'];
$return['looking']			= $_GET['seek'];
$return['dob']				= $_GET['dob'];

$return['device'] 			= $_GET['device'];
$return['city']				= $_GET['city'];
$return['lander']			= $_GET['lander'];
$return['http']				= $_GET['http'];
$return['index_id']			= $_GET['index_id'];
$return['antifraud']		= isset($_GET['antifraud']) ? json_encode($_GET['antifraud']) : '';

// Send Data to Log API
$log	= new Log($return);
$send	= $log->send($config->log->endpoint);

echo json_encode($return);