<?php
$config = file_exists('./config/api.json') ? json_decode(file_get_contents('./config/api.json')) : null;


class Log {

    private $data;

    public function __construct($data){
        $this->data = $data;
    }


    public function send($endpoint){
        $data = $this->data;

        $ch = curl_init($endpoint);
        curl_setopt_array($ch, array(
            CURLOPT_POST	=> true,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json'
            ),
            CURLOPT_POSTFIELDS => json_encode($data)
        ));

        $response = curl_exec($ch);
        $responseData = json_decode($response, true);
        $httpcode = curl_getinfo($ch, CURLINFO_RESPONSE_CODE);
        curl_close($ch);

        return $response;
    }

}