<script setup>
import { defineProps, ref } from "vue";

const props = defineProps(["location", "language"]);
const city = ref("");
const loading = ref(10);

const inteval = setInterval(function () {
  if (props.location != null && props.location.length > 0) {
    city.value = props.location ? props.location : " your area";
    clearInterval(inteval);
  }
}, 3500);

const progress = setInterval(function () {
  loading.value += 8;
  if (city.value) {
    clearInterval(progress);
  }
}, 100);

// Fixed number of girls set to 52
const found = 52;
</script>

<template>
  <div class="text-center">
    <div class="mb-1" v-if="!city">
      <div class="text-white fs-3"><i class="fa-solid fa-location-dot"></i></div>
      <div class="searching fs-6 text-white fw-light">{{ language.gps_text_3 }}</div>
    </div>
    <div class="mb-1" v-else>
      <div class="text-white fs-3"><i class="fa-solid fa-venus"></i></div>
      <div class="searching fs-6 text-white fw-light">{{ found }} {{ language.gps_text_2 }} {{ city }}</div>
    </div>
  </div>
</template>

<style scoped>
.searching {
  font-size: 0.98em !important;
}

.text-white {
  color: rgb(139, 16, 36) !important;
}
</style>
