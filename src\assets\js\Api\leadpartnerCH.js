import axios from "axios";
import endpoints from "../config/endpoints.json";

const leadpartnerch = async (formData) => {
    const httpData = {
        email           : formData.email,
        gender          : formData.gender,
        username        : formData.username,
        password        : formData.password,
        dob             : formData.birth_year + "-" + formData.birth_month + "-" + formData.birth_day,
        source_id       : formData.source_id,
        click_id        : formData.click_id,
        key             : formData.key,
        is_sellable     : !formData.is_sellable,
        device          : formData.device,
        lander          : formData.lander,
        http            : formData.http,
        seek            : formData.seek,
        t1_t2           : formData.cmp,
        offer           : "ch",
        looking         : formData.seek,
        city            : formData.location,
        zip             : formData.postal_code,
        country         : formData.country,
        index_id        : "55",
        traf_id         : formData.traf_id,
        antifraud   : formData.antifraud,
    };

    return await axios.get(endpoints.leadpartner, { params: httpData });
};

export default leadpartnerch;
