<?php
include_once('./log.php');

$config = file_exists('./config/api.json') ? json_decode(file_get_contents('./config/api.json')) : null;

/**
 * Make sure the configuration is not missing or empty
 */
if($config == null){
	$fileMissing = json_encode([
		'code' => 401,
		'message' => 'Api configuration file missing',
	]);
	die($fileMissing);
}

$startTime = microtime(true);

$md5Hash = $_GET['traf_id'] ? md5($_GET['traf_id']) : "";
$traf_id = $md5Hash ? substr($md5Hash, 0, 16) : ""; 


// Current data array commented out
$data = [
	'countryCode' => strtoupper($_GET['device']['geo']['country_code']),
	'email' => $_GET['email'],
	'gender' => $_GET['gender'],
	'dateOfBirth' => $_GET['birth_year']."-".$_GET['birth_month']."-".$_GET['birth_day'],
	'username' => $_GET['username'],
	'password' => $_GET['password'],
	'ip' => $_GET['device']['ip'],
	'city' => $_GET['device']['geo']['city'],
	'userAgent' => $_GET['device']['raw'],
	'ref' => $_GET['click_id'],
	'sub1' => $traf_id,
	'sub2' => $_GET['t1']."_".$_GET['t2']
];


// Dummy test data
// $data = [
//     'countryCode' => 'CA',
//     'email' => '<EMAIL>',
//     'gender' => 'male',
//     'dateOfBirth' => '1973-01-01',
//     'username' => 'testuser123',
//     'password' => 'testpass123',
//     'ip' => '*************',
//     'city' => 'Toronto',
//     'userAgent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
//     'ref' => 'test_click_123',
//     'sub1' => 'test_sub1',
//     'sub2' => 'test_t1_test_t2'
// ];


$endpoint = $config->flirtkings->endpoint;
$username = $config->flirtkings->username;
$password = $config->flirtkings->password;


$ch = curl_init($endpoint);
curl_setopt_array($ch, array(
    CURLOPT_POST => true,
    CURLOPT_POSTFIELDS => json_encode($data),
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_FOLLOWLOCATION => true,
    CURLOPT_HTTPHEADER => array(
        'Content-Type: application/json'
    ),
    CURLOPT_USERPWD => $username . ":" . $password,
    CURLOPT_HTTPAUTH => CURLAUTH_BASIC
));

$response = curl_exec($ch);
$responseData = json_decode(trim($response), true);
$httpcode = curl_getinfo($ch, CURLINFO_RESPONSE_CODE);
curl_close($ch);
// Do not include Email in data storage
$data['email'] = '************';

$return = [
	'code' => 200,
	'message' => '',
	'url' => '',
	'time' => '0',
	'request_data' => $data,
	'response_data' => '',
	'source' => 'flirtkings - ' . $_GET['offer']
];


/**
 * 1. Check if user created successfully - build success response
 * 2. If user creation failed - build error response
 */

if(isset($responseData['status']) && $responseData['status'] == "OK"){
	$return['message'] = $responseData['status'];
	$return['code'] = 200;
	$return['url'] = $responseData['redirectUrl'];
}
else{
	$return['message'] = $responseData['error'];
	$return['code'] = 400;
}

$endTime = microtime(true);
$return['key']				= $_GET['key'];
$return['response_data']	= $responseData;
$return['start_time']		= $startTime;
$return['end_time']			= $endTime;
$return['endpoint']			= $config->flirtkings->endpoint;
$return['time'] 			= number_format( ( $endTime - $startTime ), 2);
$return['click_id']			= $_GET['click_id'];
$return['source_id']		= $_GET['source_id'];
$return['is_sellable']		= $_GET['is_sellable'];

$return['email']			= $_GET['email'];
$return['username']			= $_GET['username'];
$return['password']			= $_GET['password'];
$return['gender']			= $_GET['gender'];
$return['looking']			= $_GET['looking'];
$return['dob']				= $_GET['birth_year'].'-'.$_GET['birth_month'].'-'.$_GET['birth_day'];

$return['device']			= $_GET['device'];
$return['city']				= $_GET['device']['geo']['city'];
$return['lander']			= $_GET['lander'];
$return['http']				= $_GET['http'];
$return['index_id']			= $_GET['index_id'];
$return['antifraud']		= isset($_GET['antifraud']) ? json_encode($_GET['antifraud']) : '';

// Send Data to Log API
$log	= new Log($return);
$send	= $log->send($config->log->endpoint);

echo json_encode($return);