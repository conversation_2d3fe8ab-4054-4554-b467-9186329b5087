import axios from 'axios'
import endpoints from '../config/endpoints.json'
import params from '../helper/urlParameters.js'

const adveryus = async (formData) => {    

    console.log(formData)

    const httpData = {
        username    : formData.username,
        email       : formData.email,
        password    : formData.password,
        birthdate   : formData.birth_year+'-'+formData.birth_month+'-'+formData.birth_day,
        gender      : formData.gender,
        city        : formData.location,
        zip         : formData.postal_code,
        country     : formData.country_code,
        click_id    : formData.click_id,
        key         : formData.key,
        source_id   : formData.source_id,
        is_sellable : !formData.is_sellable,
        device      : formData.device,
        lander      : formData.lander,
        http        : formData.http,
        s3          : formData.s3,
        looking     : formData.seek,
        traf_id     : formData.traf_id,
        index_id    : "212",
        offer       : "us",
        t1          : formData.t1,
        antifraud   : formData.antifraud,
    }

    return await axios.get(endpoints.advery, { params: httpData })
}

export default adveryus