<?php
include_once('./log.php');

$config = file_exists('./config/api.json') ? json_decode(file_get_contents('./config/api.json')) : null;

$startTime = microtime(true);

/**
 * Make sure the configuration is not missing or empty
 */
if($config == null){
	$fileMissing = json_encode([
		'code' => 401,
		'message' => 'Api configuration file missing',
	]);
	die($fileMissing);
}


$langual = isset($_GET['l']) && !empty($_GET['l']) ? $_GET['l'] : 'us';

if(isset($_GET['antifraud']) && isset($_GET['antifraud']['FraudLogix'])){
	$riskScore = $_GET['antifraud']['FraudLogix']['RiskScore'];
	if (in_array($riskScore, ['High', 'Extreme'])) {
        $langual = 'fl_extreme';
    }
}

if(isset($_GET['alreadyRegistered']) && $_GET['alreadyRegistered'] == 'true'){
	$langual = 'fl_extreme';
}

$endpoint = $config->smartlink->endpoint->$langual;


if(isset($_GET['source_id'])){
	$endpoint = str_replace('{affiliate_id}', $_GET['source_id'], $endpoint);
}if(isset($_GET['click_id'])){
	$endpoint = str_replace('{click_id}', $_GET['click_id'], $endpoint);
}

if (strpos($endpoint, '?') === false && strpos($endpoint, '&') !== false) {
	$endpoint = preg_replace('/&/', '?', $endpoint, 1);
}

$data = [
    'username'  => $_GET['username'],
    'password'  => $_GET['password'],
    'email'     => '*********',
    'sex'       => $_GET['sex'],
    'birthdate' => $_GET['birthdate'],
    'ip'        => $_SERVER['REMOTE_ADDR'],
    'var1'      => $_GET['t1_t2'],
    'endpoint'  => $endpoint
];

// Do not include Email in data storage
$data['email'] = '************';

$return = [
	'code' => 200,
	'message' => '',
	'url' => $endpoint,
	'time' => '0',
	'request_data' => $data,
	'response_data' => '',
	'source' => 'Smartlink'
];

$endTime = microtime(true);
$return['key']				= $_GET['key'];
$return['response_data']	= '{"Not Applicable - Direct Redirection"}';
$return['start_time']		= $startTime;
$return['end_time']			= $endTime;
$return['endpoint']			= $endpoint;
$return['time']				= number_format( ( $endTime - $startTime ), 2);
$return['click_id']			= $_GET['click_id'];
$return['source_id']		= $_GET['source_id'];
$return['is_sellable']		= false;

$return['email']			= $_GET['email'];
$return['username']			= $_GET['username'];
$return['password']			= $_GET['password'];
$return['gender']			= $_GET['sex'];
$return['looking']			= $_GET['looking'];
$return['dob']				= $_GET['birthdate'];

$return['device']			= $_GET['device'];
$return['lander']			= $_GET['lander'];
$return['city']				= $_GET['city'];
$return['http']				= $_GET['http'];
$return['antifraud']		= isset($_GET['antifraud']) ? json_encode($_GET['antifraud']) : '';

// Send Data to Log API
$log	= new Log($return);
$send	= $log->send($config->log->endpoint);

echo json_encode($return);