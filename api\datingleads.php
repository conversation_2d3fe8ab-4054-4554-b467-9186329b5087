<?php
include_once('./log.php');

$config = file_exists('./config/api.json') ? json_decode(file_get_contents('./config/api.json')) : null;

/**
 * Make sure the configuration is not missing or empty
 */
if($config == null){
	$fileMissing = json_encode([
		'code' => 401,
		'message' => 'Api configuration file missing',
	]);
	die($fileMissing);
}

$startTime = microtime(true);

$gender = [
	'male' => 1,
	'female' => 2,
	'couple' => 3
];

$s_gender = $_GET['gender'];
$l_gender = $_GET['looking'];

$md5Hash = $_GET['traf_id'] ? md5($_GET['traf_id']) : "";
$traf_id = $md5Hash ? substr($md5Hash, 0, 16) : ""; 


$data = [
	'clientIp'				=> $_GET['device']['ip'],
	'email' 				=> $_GET['email'],
	"gender"				=> $gender[$s_gender],
	"gender_preference"		=> $gender[$l_gender],
	"tos"					=> '1',
	// optional
	// 'username' 				=> $_GET['username'],
	// 'password' 				=> $_GET['password'],
    // 'country'   			=> strtoupper($_GET['device']['geo']['country_code']),
	// "city"					=> $_GET['device']['geo']['city'],
	// "birthdate"				=> $_GET['birth_year'].'-'.$_GET['birth_month'].'-'.$_GET['birth_day'],
	// "device"				=> $_GET['device']['device'],
];


$data_params = [
	'api_key' => $config->datingleads->api_key,
	'api_user' => $config->datingleads->api_id,
	'campaignId' => $traf_id,
	'country' => $_GET['device']['geo']['country_code'],
	'flow' => 'SOI',
	'var3' => $_GET['click_id'],
	'var1' => $_GET['t1'],
	'var2' => $_GET['t2'],
];



$endpoint = $config->datingleads->endpoint;
$endpoint = $endpoint .'?'.http_build_query($data_params);


$ch = curl_init($endpoint);
curl_setopt_array($ch, array(
    CURLOPT_POST => true,
    CURLOPT_POSTFIELDS => json_encode($data),
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_FOLLOWLOCATION => true,
    CURLOPT_HTTPHEADER => array(
        'Content-Type: application/json'
    )
));

$response = curl_exec($ch);
$responseData = json_decode(trim($response), true);
$httpcode = curl_getinfo($ch, CURLINFO_RESPONSE_CODE);
curl_close($ch);

// Do not include Email in data storage
$data['email'] = '************';

$return = [
	'code' => 200,
	'message' => '',
	'url' => '',
	'time' => '0',
	'request_data' => $data,
	'response_data' => '',
	'source' => 'Datingleads - ' . $_GET['offer']
];


/**
 * 1. Check if user created successfully - build success response
 * 2. If user creation failed - build error response
 */
if($response != false){
	if($responseData['code'] == 200){
		$return['message'] = $responseData['status'];
		$return['code'] = 200;
		$return['url'] = $responseData['data']['redirect_url'];
	}
	else{
		$return['message'] = $responseData['data']['errors'];
		$return['code'] = 400;
	}
}else{
	$responseData = 'Unable to connect API';
}

$endTime = microtime(true);
$return['key']				= $_GET['key'];
$return['response_data']	= $responseData;
$return['start_time']		= $startTime;
$return['end_time']			= $endTime;
$return['endpoint']			= $config->datingleads->endpoint;
$return['time'] 			= number_format( ( $endTime - $startTime ), 2);
$return['click_id']			= $_GET['click_id'];
$return['source_id']		= $_GET['source_id'];
$return['is_sellable']		= $_GET['is_sellable'];

$return['email']			= $_GET['email'];
$return['username']			= $_GET['username'];
$return['password']			= $_GET['password'];
$return['gender']			= $_GET['gender'];
$return['looking']			= $_GET['looking'];
$return['dob']				= $_GET['birth_year'].'-'.$_GET['birth_month'].'-'.$_GET['birth_day'];

$return['device']			= $_GET['device'];
$return['city']				= $_GET['device']['geo']['city'];
$return['lander']			= $_GET['lander'];
$return['http']				= $_GET['http'];
$return['index_id']			= $_GET['index_id'];
$return['antifraud']		= isset($_GET['antifraud']) ? json_encode($_GET['antifraud']) : '';

// Send Data to Log API
$log	= new Log($return);
$send	= $log->send($config->log->endpoint);

echo json_encode($return);