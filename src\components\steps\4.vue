<script setup>
import { ref } from 'vue'
import Params from '../../assets/js/helper/urlParameters.js'
import Api from '../../assets/js/helper/api.js'

const props = defineProps(['step', 'inputData', 'previous', 'language', 'country'])

const apiErrorMessage = ref(false)
const disableSubmitBtn = ref(false)

const validatePassword = (x) => {
    if(x.length > 14 || x.length < 6 || validator(x) == false || explicitValidator(x)){
        apiErrorMessage.value = props.language.password_error_2
        return false
    }else{
        apiErrorMessage.value = false
        return true
    }
}

const validateUsername = (x) => {    
    if(x.length > 14 || x.length < 6 || validator(x) == false || explicitValidator(x)){
        apiErrorMessage.value = props.language.username_error_2
        return false
    }
    else{
        apiErrorMessage.value = false
        return true
    }    
}

const submitForm = () => {
    
    if(validateUsername(props.inputData.username.value) != true){
        return false
    }else if(validatePassword(props.inputData.password.value) != true ){
        return false
    }else if(props.inputData.privacy.value == false){
        apiErrorMessage.value = props.language.privacy_text_2
        return false
    }else{
        // Send data to API
        let formData = {}
        for(let x in props.inputData) {
            formData[x] = props.inputData[x]['value']
        }

        // call to API adapter
        Api(formData, disableSubmitBtn, apiErrorMessage)
    }
    
}

const validator = (str) => {
    return /[0-9][a-zA-Z]|[a-zA-Z][0-9]/.test(str);
}

const explicitValidator = (str) => {
    const format =  /[ `!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~]/
    return format.test(str)
}

</script>

<template>
    
    <div class="card text-center t-bg mx-auto" v-if="step == 4">
        <div class="card-body">
            <!-- Final -->
            <div class="col-md-12 mt-3">
                <h5 class="card-title text-pink fs-6">{{ language.username }}</h5>
                <p class="text-secondary fw-lighter fs-6" style="font-size: 0.7em !important">{{ language.text_only }}</p>
            </div>
            <div class="col-md-12 p-2">
                <div class="input-group flex-nowrap">
                    <span class="input-group-text" id="addon-wrapping">@</span>
                    <input type="text" name="username" class="form-control" v-model="props.inputData.username.value" @keyup="validateUsername(props.inputData.username.value)">
                </div>
            </div>
            <div class="col-md-12 pt-2">
                <h5 class="card-title text-pink fs-6">{{ language.password }}</h5>
                <p class="text-secondary fw-lighter fs-6" style="font-size: 0.7em !important">{{ language.text_only }}</p>
            </div>
            <div class="col-md-12 p-2">
                <div class="input-group flex-nowrap">
                    <span class="input-group-text" id="addon-wrapping">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-lock" viewBox="0 0 16 16">
                            <path d="M8 1a2 2 0 0 1 2 2v4H6V3a2 2 0 0 1 2-2zm3 6V3a3 3 0 0 0-6 0v4a2 2 0 0 0-2 2v5a2 2 0 0 0 2 2h6a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2zM5 8h6a1 1 0 0 1 1 1v5a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1V9a1 1 0 0 1 1-1z"/>
                        </svg>
                    </span>
                    <input type="password" name="password" class="form-control" aria-label="Username" aria-describedby="addon-wrapping" v-model="props.inputData.password.value" @keyup="validatePassword(props.inputData.password.value)">
                </div>
            </div>
            <div class="col-md-12 pt-2">
                <p class="txt-grey text-center">
                    <input class="form-check-input" type="checkbox" id="privacy" value="privacy" v-model="props.inputData.privacy.value" @change="apiErrorMessage = false">
                    <label for="privacy" class="ps-2">{{ language.privacy_agree }}</label>
                </p>
                <p class="txt-grey mb-0" style="font-size: 11px">
                    <a href="/terms" target="_blank" class="text-dark text-decoration-none">{{ language.privacy_text }} </a> 
                </p>
                <p class="txt-grey text-center mt-0" v-if="country == 'us'">
                    <input class="form-check-input align-middle" type="checkbox" id="is_sellable" value="true" v-model="props.inputData.is_sellable.value"  style="font-size: 9px">
                    <label for="is_sellable" class="ps-2 align-middle" style="font-size: 9px">{{ language.data_permission }}</label>
                </p>
            </div>
            <div class="col-md-12 px-2">
                <div class="border-danger border-bottom m-2 px-2 text-danger fs-6" style="font-size: 0.8em !important" v-show="apiErrorMessage">{{ apiErrorMessage }}</div>
                <div class="info text-white fs-6" v-show="disableSubmitBtn">{{ language.validating }}</div>
                <button class="btn btn-light fw-bold me-2 text-uppercase" type="button"  @click="previous" :class="`${disableSubmitBtn ? 'disabled' : ''}`">{{ language.btn_previous }}</button>
                <button class="btn next-btn fw-bold text-white text-uppercase"  type="button"  @click="submitForm()" :class="`${disableSubmitBtn ? 'disabled' : ''}`">
                    {{ language.btn_next }}
                    <div class="spinner-border spinner-grow-sm text-light" v-show="disableSubmitBtn" role="status">
                        <span class="visually-hidden">{{ language.loading }}</span>
                    </div>
                </button>
                
            </div>
            <!-- Final end -->
        </div>
    </div>

</template>

<style>
</style>