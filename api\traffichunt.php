<?php
include_once('./log.php');
include_once('./helpers/execution_time.php');

class TrafficHunt {


	private $config;
	private $data;
	private $return;
	private $geoISO;
	private $params;
	private $site_config;
	private $execStats;
	private $httpcode;
	private $currency;
	private $bidCode;

	private $bidRequestCode;
	private $bidPlacementCode;
	private $bidRedirectionUrl;
	private $bidPlacementUrl;
	
	public function __construct() {
		$this->config	= file_exists('./config/api.json') ? json_decode(file_get_contents('./config/api.json')) : null;
		
		if($this->config == null){
			$fileMissing = json_encode([
				'code' => 401,
				'message' => 'Api configuration file missing',
			]);
			die($fileMissing);
		}
	}

	
	public function getBidPrice($params) {

		$this->params		= $params;
		$this->geoISO		= $params->device->geo->country_code;
		$device				= $params->device;
		$this->site_config	= isset($this->config->RTB->TrafficHunt->{$params->device->geo->country_code}) ? $this->config->RTB->TrafficHunt->{$params->device->geo->country_code} : $this->config->RTB->TrafficHunt->WW;
		$traf_id			= isset($params->traf_id) ? substr(md5($params->traf_id), 0, 16) : "";
		$s1					= !empty($traf_id) && isset($params->source_id) ? $traf_id . "_" . $params->source_id : $traf_id;
		
        $this->data = [
			"id"	=> $this->params->click_id,
			"at"	=> 2,
			"tmax"	=> 120,
			"imp"	=> [
			  [
				"id"		=> $this->params->click_id,
			  ]
			],
			"site"		=> [
				"id"		=> $this->site_config->id,
				"name"		=> $this->site_config->name,
				"domain"	=> $this->site_config->domain,
				"page"		=> $this->site_config->page,
				"ref"		=> $this->site_config->domain,
			],
			"device"		=> [
				"ip"			=> $device->ip,
				"ua"			=> $device->raw,
				"js"			=> 1,
				"devicetype"	=> 2
			],
			"user"	=> [
			  "id"	=>  md5(microtime($params->email)),
			  "buyeruid"	=> $this->params->click_id
			]
		];


		// Add Tier 3 countries
		if(isset($this->params->rules->price) && !is_null($this->params->rules->price) && (float)$this->params->rules->price > 0){
			$this->data['imp'][0]['bidfloor']		= $params->rules->price;
			$this->currency = "USD";
		}

		return $this->initiateBidPriceCall();
	}

	
	private function initiateBidPriceCall() {
		$url_end = "http://rtb.traffichunt.com/adx-dir-d/openrtb/bidrequest_v2?feed=3307";
		$ch = curl_init($url_end);
		curl_setopt_array($ch, array(
			CURLOPT_POST    => true,
			CURLOPT_SSL_VERIFYHOST	=> false,
			CURLOPT_SSL_VERIFYPEER	=> false,
			CURLOPT_RETURNTRANSFER	=> true,
			CURLOPT_HTTPHEADER => array(
				'Accept: application/json',
                'x-openrtb-version: 2.3',
				'Content-Type: application/json'
			),
			CURLOPT_POSTFIELDS => json_encode($this->data)
		));

		$response			= curl_exec($ch);
		$responseData		= json_decode($response, TRUE);
		$this->httpcode		= curl_getinfo($ch, CURLINFO_RESPONSE_CODE);

		curl_close($ch);

		try {
			$url = "https://trakle01.online/api/rtb-store";

			$data = [
				"name" => "Traffic Hunt",
				"url" => $url_end,
				"request_data" => $this->data,
				"response_data" => $responseData,
				"request_type" => "POST"
			];

			$ch = curl_init($url);

			curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
			curl_setopt($ch, CURLOPT_POST, true);
			curl_setopt($ch, CURLOPT_HTTPHEADER, [
				'Content-Type: application/json'
			]);
			curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));

			$response = curl_exec($ch);

			$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
			curl_close($ch);

		} catch (Exception $e) {
			// Intentionally left blank
		}

		if($this->httpcode == 200){
			$this->bidRedirectionUrl	= $responseData['seatbid'][0]['bid'][0]['adm'] ?? false;
			$this->bidPlacementUrl		= $responseData['seatbid'][0]['bid'][0]['nurl'] ?? false;
		}
		
		return json_decode(json_encode($responseData));
	}

	public function initiatePlacingBid($responseData){
		return $this->handleResponse($responseData);
	}

	
	private function handleResponse($responseData) {
		$this->return = [
			'code' => 200,
			'message' => '',
			'url' => '',
			'time' => '0',
			'request_data' => $this->data,
			'response_data' => '',
			'source' => 'TrafficHunt - '.$this->geoISO
		];
		
		
		if($this->httpcode == 204){
			$this->return['message']	= 'No bid available with floor bid price';
			$this->return['code']		= $this->httpcode;
		}else if($this->httpcode == 400){
			$this->return['message']	= 'No bid available with user data/geo data';
			$this->return['code']		= $this->httpcode;
		}else if($this->httpcode == 200){
			$this->return['message']  = 'Bid placed successfully';
			$this->return['url']      = $responseData->seatbid[0]->bid[0]->adm;
			$this->return['code']     = $this->httpcode;
			
			// $this->confirmBid($responseData);
			$this->sendPostback($responseData);
		}else{
			$this->return['message']  = 'Unknown Error from RTB';
			$this->return['code']     = $this->httpcode;
		}
		
		return $this->prepareReturnData($responseData);
	}

	
	private function confirmBid($responseData) {
		if($this->httpcode == 200){
			$copen = curl_init($this->bidPlacementUrl);
			curl_setopt($copen, CURLOPT_SSL_VERIFYHOST, false);
			curl_setopt($copen, CURLOPT_SSL_VERIFYPEER, false);
			curl_setopt($copen, CURLOPT_RETURNTRANSFER, true);
			curl_setopt($copen, CURLOPT_HTTPHEADER, [
				'x-openrtb-version: 2.4',
				'Connection: Keep-Alive',
				'Content-Type: application/json'
			]);
			curl_exec($copen);
			$this->bidPlacementCode = curl_getinfo($copen, CURLINFO_RESPONSE_CODE);
			curl_close($copen);

			// Check and see if bid placement is successful
			if($this->bidPlacementCode == 200){
				// ADM Trigger click URL
				$this->return['url'] = $this->bidRedirectionUrl;
				return true;
			}

			return false;
		}
	}
	
	
	private function sendPostback($responseData) {
		// if($this->bidPlacementCode == 200){
			
			$postbackUrl = "https://trakle02.online/postback?id=" . urlencode($this->params->click_id) . 
				"&payout=" . urlencode($responseData->seatbid[0]->bid[0]->price) . 
				"&currency=USD" .
				"&src=" . urlencode("traffichunt").
				"&type=" . urlencode("rtb").
				"&country_code=" . urlencode($this->geoISO);

			$postbackCurl = curl_init();
			curl_setopt($postbackCurl, CURLOPT_URL, $postbackUrl);
			curl_setopt($postbackCurl, CURLOPT_SSL_VERIFYHOST, false);
			curl_setopt($postbackCurl, CURLOPT_SSL_VERIFYPEER, false);
			curl_setopt($postbackCurl, CURLOPT_RETURNTRANSFER, true);
			curl_exec($postbackCurl);
			curl_close($postbackCurl);
		// }
	}

	
	private function prepareReturnData($responseData) {

		$this->execStats	= EXEC_TIME->stats();
		$this->return['key'] 				= $this->params->key;
		$this->return['response_data'] 		= $responseData;
		$this->return['start_time'] 		= $this->execStats->start;
		$this->return['end_time'] 			= $this->execStats->end;
		$this->return['time'] 				= $this->execStats->duration;
		$this->return['endpoint'] 			= $this->site_config->endpoint;
		$this->return['click_id']         	= $this->params->click_id;
		$this->return['source_id']        	= $this->params->source_id;
		$this->return['is_sellable']      	= $this->params->is_sellable;
		$this->return['email']            	= $this->params->email;
		$this->return['username']         	= $this->params->username;
		$this->return['password']         	= $this->params->password;
		$this->return['gender']           	= $this->params->gender;
		$this->return['looking']          	= $this->params->seek;
		$this->return['dob']              	= $this->params->birth_year .'-'.$this->params->birth_month .'-'.$this->params->birth_day;
		$this->return['device']           	= $this->params->device;
		$this->return['city']             	= $this->params->location;
		$this->return['lander']           	= $this->params->lander;
		$this->return['http']             	= $this->params->http;
		$this->return['antifraud']			= isset($_GET['antifraud']) ? json_encode($_GET['antifraud']) : '';
		
		// Send Data to Log API
		$log    = new Log($this->return);
		$send   = $log->send($this->config->log->endpoint);
		
		return json_encode($this->return);
	}


}


define('TrafficHunt', new TrafficHunt);