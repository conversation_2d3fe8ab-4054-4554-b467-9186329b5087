import axios from 'axios'
import endpoints from '../../../assets/js/config/endpoints.json'
import params from '../../../assets/js/helper/urlParameters.js'
import device from '../../../assets/js/helper/deviceDetection.js'

const huge = async (formData) => {    

    let httpData = {
        username        : formData.username,
        email           : formData.email,
        gender          : formData.gender,
        birth_year      : formData.birth_year,
        birth_month     : formData.birth_month,
        birth_day       : formData.birth_day,
        mobile          : device().device == 'Mobile' ? 1 : 0,
        click_id        : formData.click_id,
        key             : formData.key, // Unique Identifier for Logger
        source_id       : formData.source_id,
        is_sellable     : !formData.is_sellable,
        device          : formData.device,
        lander          : formData.lander,
        http            : formData.http,
        cmp             : formData.cmp,
        password        : formData.password,
        looking         : formData.seek,
        city            : formData.location,
        index_id        : "4",
        traf_id         : formData.traf_id,
        antifraud   : formData.antifraud,
    }

    return await axios.get(endpoints.huge_traffic, { params: httpData })

}

export default huge