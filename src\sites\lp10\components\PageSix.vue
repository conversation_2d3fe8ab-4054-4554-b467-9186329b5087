<script setup>
import { ref, defineProps, inject, onMounted } from "vue";
import Steps from "../../lp9/components/views/StepsMarker.vue";
import Language from "../../../assets/js/helper/Language.js";
import Swal from "sweetalert2";
import Searching from "../../../sites/lp12/components//views/Searching.vue";

const props = defineProps(["steps", "moveNextSlide", "moveBackSlide", "language", "location", "inputs"]);
const steps = ref(props.steps);
const validEmail = ref(false);
const assets = inject("assets");

const validateEmail = () => {
  const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  validEmail.value = emailPattern.test(props.inputs.email.value);
};

const continueToNextSlide = () => {
  validateEmail();
  const alertMessage = document.getElementById("alert-message").innerText;

  if (!validEmail.value) {
    Swal.fire({
      text: alertMessage,
      customClass: {
        popup: "custom-swal-popup",
      },
    });
  } else {
    props.moveNextSlide();
  }
};

onMounted(() => {
  const urlParams = new URLSearchParams(window.location.search);
  const emailFromParams = urlParams.get("email");

  if (emailFromParams) {
    props.inputs.email.value = emailFromParams;
    setTimeout(() => {
      const emailInput = document.getElementById("email");
      if (emailInput) {
        emailInput.value = emailFromParams;
      }
    }, 0);
  }
});

const handleKeyPress = (event) => {
  if (event.key === "Enter") {
    continueToNextSlide();
  }
};
</script>

<template>
  <div class="container my-5">
    <div class="card p-4">
      <div class="header-logo">
        <img src="../../../assets/flirty.png" class="logofli" alt="" />
      </div>
      <form>
        <div class="form-group">
          <label for="gender" class="w-100 text-center fs-5">{{ Language.email_text }}</label>
          <hr />
          <div class="d-flex justify-content-around my-3">
            <div class="d-flex justify-content-center flex-column gap-3">
              <input v-model="props.inputs.email.value" id="email" class="form-control rounded-5 px-5" placeholder="e.g. <EMAIL>" type="text" @focus="onFocus" @blur="onBlur" @keypress="handleKeyPress" />
              <button type="button" class="btn border btn-outline-12 rounded-5 px-5" for="option2" @click="continueToNextSlide">{{ Language.continue }}</button>
              <div class="text-center">
                <p class="text-dark" style="font-size: 10px">{{ Language.spam_alert }}</p>
              </div>
            </div>
          </div>
        </div>
      </form>
      <div id="alert-message" style="display: none">
        {{ Language.email_error }}
      </div>
      <Steps :step="steps" />
      <div class="disclaimer mt-2">
        <div class="progress mb-3" role="progressbar" aria-label="Info example" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100">
          <div class="progress-bar bg-info" style="width: 84%"></div>
        </div>
        <div class="d-flex justify-content-center">
          <Searching :location="location" :language="Language" />
        </div>
      </div>
    </div>
  </div>
</template>

<style>
body {
  background-color: #f0f8ff;
}
.card {
  max-width: 400px;
  margin: auto;
  padding: 20px;
  border-radius: 15px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}
.header-logo {
  text-align: center;
  margin-bottom: 20px;
}
.header-logo img {
  width: 150px;
}
.header-text {
  text-align: center;
  font-size: 1.5rem;
  margin-bottom: 30px;
}
.form-group {
  margin-bottom: 15px;
}
.btn-primary {
  background-color: #6a1b9a;
  border-color: #6a1b9a;
  font-size: 1.2rem;
}
.disclaimer {
  text-align: center;
  margin-top: 20px;
}
.disclaimer small {
  color: #6c757d;
}

.btn-outline-12 {
  border-color: #6a1b9a;
  color: black;
}

.btn-outline-12:hover {
  background-color: #6a1b9a;
  color: white;
}

.styled-paragraph {
  font-style: italic;
  font-size: 1rem;
  color: palevioletred !important;
}

.card {
  height: 30em;
  width: 25em;
}
</style>
