<script setup>
import { ref } from "vue";
import Params from "../../assets/js/helper/urlParameters.js";

const url = Params().url.replace("?", "");

defineProps(["step", "next", "inputData", "language"]);
</script>

<template>
  <div class="card text-center t-bg mx-auto" v-if="step == 2">
    <div class="card-body">
      <div class="mt-auto text-center slides rounded card-black p-2">
        <div class="mb-2">
          <h5 class="card-title text-pink fs-6 text-uppercase">{{ language.sex_text }}</h5>
        </div>
        <select class="form-select mb-3" name="gender" v-model="inputData.gender.value">
          <option value="male">{{ Language.guy }}</option>
          <option value="female">{{ Language.girl }}</option>
        </select>
        <div class="col-md-12">
          <h5 class="card-title text-pink fs-6 text-uppercase">{{ language.looking }}</h5>
        </div>
        <div class="col-md-12 mb-4">
          <select class="form-select" name="seek" v-model="inputData.seek.value">
            <option value="female">{{ Language.girl }}</option>
            <option value="male">{{ Language.guy }}</option>
          </select>
        </div>
        <div class="col-md-12 p-2">
          <button class="btn next-btn fw-bold text-white text-uppercase" type="button" @click="next">{{ language.btn_next }}</button>
        </div>
      </div>
    </div>
  </div>
</template>

<style></style>
