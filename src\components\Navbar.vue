<script setup>
import Timer from "../components/CountDown.vue";
import Language from "../assets/js/helper/Language";
import { inject } from "vue";

const assets = inject("assets");
</script>

<template>
  <nav class="d-flex justify-content-between px-3">
    <div class="icon mt-2">
      <img :src="assets + 'sexymeetupss.png'" style="height: 3.5em" alt="" />
    </div>
    <div class="timer">
      <Timer :language="Language" />
    </div>
  </nav>
</template>
