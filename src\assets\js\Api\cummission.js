import axios from 'axios'
import endpoints from '../../../assets/js/config/endpoints.json'
import params from '../../../assets/js/helper/urlParameters.js'

const cummission = async (formData) => {

    let httpData = {
        username    : formData.username,
        password    : formData.password,
        email       : formData.email,
        sex         : formData.gender,
        birthdate   : formData.birth_year +'-'+formData.birth_month+'-'+formData.birth_day,
        clickid     : formData.click_id,
        sourceid    : formData.source_id,
        key         : formData.key, // Unique Identifier for Logger,
        is_sellable : !formData.is_sellable,
        device      : formData.device,
        lander      : formData.lander,
        http        : formData.http,
        looking     : formData.seek,
        city        : formData.location,
        traf_id         : formData.traf_id,
        antifraud   : formData.antifraud,
    }
    //Send data to api
    return await axios.get(endpoints.cummission, { params: httpData} )
}

export default cummission