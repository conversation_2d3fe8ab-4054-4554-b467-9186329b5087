<?php
header('Access-Control-Allow-Origin: *');
require_once('./loudbids.php');
require_once('./exoclick.php');
require_once('./trafficbar.php');
require_once('./traffichunt.php');

// Add debugging
$debug = [];
$debug['timestamp'] = date('Y-m-d H:i:s');

$request    = json_decode(json_encode($_GET['data'], true));
$error      = json_encode(['code' => 400, 'message' => 'Bid price not met', 'debug' => $debug]);
$bids       = [];
$country_code = $request->device->geo->country_code;

$debug['country_code'] = $country_code;

$dictonary = (object) [
    // 'exoclick'  => ExoClick,
    'loudbids'      => LoudBids,
    // 'trafficbar'    => TrafficBar,
    'traffichunt'   => TrafficHunt
];

$json_path = '../src/assets/js/config/antifraud/rules/' . $country_code . '.json';
$json_data = json_decode(file_get_contents($json_path));
$debug['supported_bidders'] = [];
$request->rules->supported = $json_data->supported;

// Sending Bid Request
foreach($request->rules->supported as $key => $val){
    $debug['supported_bidders'][$key] = $val;

    // Check if validate is true
    // If validate is true it should have safe ip and safe email
    if($val == 'true'){
        if(property_exists($dictonary, $key)){
            $classInstance = $dictonary->$key ?? null;
            
            if ($classInstance) {
                $callBidPrice = $dictonary->$key->getBidPrice($request);
                $debug['bid_responses'][$key] = isset($callBidPrice->seatbid[0]->bid[0]->price) ? 
                    $callBidPrice->seatbid[0]->bid[0]->price : 'No valid bid';
            }
            // Check response data and add them into array for further processing
            if(!empty($callBidPrice) && $callBidPrice != null && isset($callBidPrice->seatbid[0]->bid[0]->price)){
                $callBidPrice->country_code = $country_code;
                $bids[] = [
                    'class'     => $dictonary->$key,
                    'amount'    => (float)$callBidPrice->seatbid[0]->bid[0]->price,
                    'method'    => 'initiatePlacingBid',
                    'data'      => $callBidPrice,
                    'bidder'    => $key
                ];
            }
        }
    }
}

$debug['valid_bids_count'] = count($bids);

function sendRtbClass(array $data) {
    $url = "https://trakle02.online/api/rtb-calls"; // Example URL
    
    try {
        $ch = curl_init($url);
        
        if ($ch === false) {
            throw new Exception("Failed to initialize cURL");
        }
        
        $payload = json_encode($data);
        
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            "Content-Type: application/json",
            "Content-Length: " . strlen($payload)
        ]);
        
        $response = curl_exec($ch);
        
        if ($response === false) {
            throw new Exception("cURL Error: " . curl_error($ch));
        }
        
        curl_close($ch);
        
        return $response;
    } catch (Exception $e) {
        return "Error: " . $e->getMessage();
    }
}

// Get the highest bidder
if(!empty($bids)){
    // Sort bids by amount in descending order
    usort($bids, function($a, $b) {
        return (float)$b['amount'] <=> (float)$a['amount'];
    });
    
    $highestBid = (float)$bids[0]['amount'];
    $debug['highest_bid'] = $highestBid;
    $debug['highest_bidder'] = $bids[0]['bidder'];
    
    $highestBids = array_filter($bids, function($bid) use ($highestBid) {
        return (float)$bid['amount'] == $highestBid;
    });
    
    // Read the current win counts
    $winnerCountFile = __DIR__ . '/config/bid_winners.json';
    $debug['winner_count_file'] = $winnerCountFile;
    $debug['winner_count_file_exists'] = file_exists($winnerCountFile) ? 'Yes' : 'No';
    
    $winnerCounts = [];
    
    if (file_exists($winnerCountFile)) {
        $winnerCounts = json_decode(file_get_contents($winnerCountFile), true);
        $debug['winner_counts_before'] = $winnerCounts;
    } else {
        // Create the file with default values if it doesn't exist
        $winnerCounts = [
            'exoclick' => 0,
            'loudbids' => 0,
            'trafficbar' => 0,
            'traffichunt' => 0
        ];
        
        // Ensure the config directory exists
        if (!is_dir(__DIR__ . '/config')) {
            mkdir(__DIR__ . '/config', 0755, true);
        }
        
        // Create the file
        file_put_contents($winnerCountFile, json_encode($winnerCounts, JSON_PRETTY_PRINT));
        $debug['created_new_file'] = 'Yes';
    }
    
    // If there are multiple highest bids, select based on win count
    if (count($highestBids) > 1) {
        $debug['multiple_highest_bids'] = true;
        // Find the lowest win count among the highest bidders
        $lowestCount = PHP_INT_MAX;
        $lowestCountBidders = [];
        
        foreach ($highestBids as $bid) {
            $bidderName = $bid['bidder'];
            $bidderCount = isset($winnerCounts[$bidderName]) ? $winnerCounts[$bidderName] : 0;
            
            if ($bidderCount < $lowestCount) {
                $lowestCount = $bidderCount;
                $lowestCountBidders = [$bid];
            } elseif ($bidderCount == $lowestCount) {
                $lowestCountBidders[] = $bid;
            }
        }
        
        // If multiple bidders have the same lowest count, select one randomly
        if (count($lowestCountBidders) > 1) {
            $randomIndex = array_rand($lowestCountBidders);
            $winner = $lowestCountBidders[$randomIndex];
        } else {
            $winner = reset($lowestCountBidders);
        }
    } else {
        $debug['multiple_highest_bids'] = false;
        // Only one highest bidder
        $winner = reset($highestBids);
    }
    
    $debug['winner'] = $winner['bidder'];
    $debug['winner_bid_amount'] = $winner['amount'];
    
    // Increment the winner count
    if (isset($winnerCounts[$winner['bidder']])) {
        $winnerCounts[$winner['bidder']]++;
    } else {
        $winnerCounts[$winner['bidder']] = 1;
    }
    
    $debug['winner_counts_after'] = $winnerCounts;
    
    // Save the updated counts
    $writeResult = file_put_contents($winnerCountFile, json_encode($winnerCounts, JSON_PRETTY_PRINT));
    $debug['write_result'] = $writeResult !== false ? 'Success (' . $writeResult . ' bytes)' : 'Failed';
    
    // Verify the file was updated
    if (file_exists($winnerCountFile)) {
        $verifiedCounts = json_decode(file_get_contents($winnerCountFile), true);
        $debug['verified_counts'] = $verifiedCounts;
    }

    

    $rtb_bids = [];
    foreach ($bids as $bid) {
        $rtb_bids[$bid['bidder']] = [
            'bidder'    => $bid['bidder'],
            'data'      => $bid['data']
        ];
    }

    $rtb_data = [
        'winner'    => isset($winner['bidder']) ? $winner['bidder'] : 'N/A',
        'bids'      => json_encode($rtb_bids),
        'click_id'  => isset($request->click_id) ? $request->click_id : 'no_click_id'
    ];

    sendRtbClass($rtb_data);
    
    $winnerInfo = [
        'winner' => $winner['bidder'],
        'bid_amount' => $winner['amount'],
        'win_count' => $winnerCounts[$winner['bidder']]
    ];
    
    $class = $winner['class'];
    $result = $class->{$winner['method']}($winner['data']);
    
    $originalResult = json_decode($result, true);
    $combinedResult = array_merge($originalResult, ['winner_info' => $winnerInfo, 'debug' => $debug]);
    
    echo json_encode($combinedResult);
    exit;
} else {
    echo $error;
    exit;
}