import axios from "axios";
import endpoints from "../config/endpoints.json";

const wiimax = async (formData) => {
  const httpData = {
    username: formData.username,
    password: formData.password,
    email: formData.email,
    gender: formData.gender,
    looking: formData.seek,
    countryCode: formData.country_code,
    birth_day: formData.birth_day,
    birth_month: formData.birth_month,
    birth_year: formData.birth_year,
    click_id: formData.click_id,
    source_id: formData.cmp,
    key: formData.key,
    is_sellable: !formData.is_sellable,
    device: formData.device,
    lander: formData.lander,
    media: formData.media,
    http: formData.http,
    offer: "uk",
    city: formData.location,
    t1: formData.t1,
    index_id: "209",
    traf_id: formData.traf_id,
    antifraud: formData.antifraud,
  };

  return await axios.get(endpoints.wiimax, { params: httpData });
};

export default wiimax;
