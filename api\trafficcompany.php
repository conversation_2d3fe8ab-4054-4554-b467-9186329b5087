<?php
include_once('./log.php');

$config = file_exists('./config/api.json') ? json_decode(file_get_contents('./config/api.json')) : null;

/**
 * Make sure the configuration is not missing or empty
 */
if($config == null){
	$fileMissing = json_encode([
		'code' => 401,
		'message' => 'Api configuration file missing',
	]);
	die($fileMissing);
}

$startTime = microtime(true);

$md5Hash = $_GET['traf_id'] ? md5($_GET['traf_id']) : "";
$traf_id = $md5Hash ? substr($md5Hash, 0, 16) : ""; 

$offer = $_GET['offer'];
$access_token = $config->trafficcompany->access_token;
$offer_id = $config->trafficcompany->offer_id->$offer;
$gender = 'm';
$looking = 'f';

if($_GET['gender'] == 'female'){
	$gender = 'f';
}

if($_GET['looking'] == 'male'){
	$looking = 'm';
}

$data = [
	'access-token'	        => $access_token,
	'ip'					=> $_GET['device']['ip'],
	'email' 				=> $_GET['email'],
	'password'				=> $_GET['password'],
	'lang'					=> isset($_GET['locale']) ? $_GET['locale'] : 'en',
	'gender'				=> $gender,
	'looking_for'			=> $looking,
	'user_name'				=> $_GET['username'],
	'date_of_birth'			=> $_GET['birth_year'] . '-' . $_GET['birth_month'] . '-' . $_GET['birth_day'],
	'click_id'				=> $_GET['click_id'],
	'offer'					=> (int)$offer_id,
];

$endpoint = $config->trafficcompany->endpoint;
$endpoint = $endpoint .'?'.http_build_query($data);


$ch = curl_init($endpoint);
curl_setopt_array($ch, array(
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_FOLLOWLOCATION => true,
));

$response 		= curl_exec($ch);
$responseData 	= json_decode(trim($response), true);
$httpcode 		= curl_getinfo($ch, CURLINFO_RESPONSE_CODE);
curl_close($ch);

// Do not include Email in data storage
$data['email'] = '************';


$return = [
	'code' 		=> 200,
	'message' 	=> '',
	'url' 		=> '',
	'time' 		=> '0',
	'request_data' => $data,
	'response_data' => '',
	'source' 	=> 'trafficcompany - ' . $_GET['offer']
];


/**
 * 1. Check if user created successfully - build success response
 * 2. If user creation failed - build error response
 */
if($response != false){
	if(isset($responseData['code']) && (int)$responseData['code'] == 2002 && isset($responseData['redirectUrl'])){
		$return['message'] 	= $responseData['message'];
		$return['code'] 	= 200;
		$return['url'] 		= $responseData['redirectUrl'];
	}
	else{
		$return['message'] 	= $responseData['message'];
		$return['code'] 	= isset($responseData['code']) ? $responseData['code'] : $httpcode;
	}
}else{
	$responseData = 'Unable to connect API';
}

$endTime = microtime(true);
$return['key']				= $_GET['key'];
$return['response_data']	= $responseData;
$return['start_time']		= $startTime;
$return['end_time']			= $endTime;
$return['endpoint']			= $endpoint;
$return['time'] 			= number_format( ( $endTime - $startTime ), 2);
$return['click_id']			= $_GET['click_id'];
$return['source_id']		= $_GET['source_id'];
$return['is_sellable']		= $_GET['is_sellable'];
$return['email']			= $_GET['email'];
$return['username']			= $_GET['username'];
$return['password']			= $_GET['password'];
$return['gender']			= $_GET['gender'];
$return['looking']			= $_GET['looking'];
$return['dob']				= $_GET['birth_year'].'-'.$_GET['birth_month'].'-'.$_GET['birth_day'];
$return['device']			= $_GET['device'];
$return['city']				= $_GET['device']['geo']['city'];
$return['lander']			= $_GET['lander'];
$return['http']				= $_GET['http'];
$return['index_id']			= $_GET['index_id'];
$return['antifraud']		= isset($_GET['antifraud']) ? json_encode($_GET['antifraud']) : '';

// Send Data to Log API
$log	= new Log($return);
$send	= $log->send($config->log->endpoint);

echo json_encode($return);