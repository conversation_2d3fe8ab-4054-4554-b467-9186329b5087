import axios from 'axios'
import endpoints from '../../../assets/js/config/endpoints.json'

const ais = async (formData) => {

    let httpData = {
        username        : formData.username,
        password        : formData.password,
        email           : formData.email,
        gender          : formData.gender,
        dob             : formData.birth_year + "-" + formData.birth_month + "-" + formData.birth_day,
        key             : formData.key,
        click_id        : formData.click_id,
        source_id       : formData.source_id,
        is_sellable     : !formData.is_sellable,
        device          : formData.device,
        lander          : formData.lander,
        http            : formData.http,
        offer           : 'us',
        cmp             : formData.cmp,
        t1              : formData.t1,
        seek            : formData.seek,
        img             : formData.image,
        city            : formData.location,
        index_id        : "3",
        traf_id         : formData.traf_id,
        antifraud   : formData.antifraud,

    }

    return await axios.get(endpoints.ais, { params: httpData })
}

export default ais