import axios from 'axios'
import endpoints from '../config/endpoints.json'
import params from '../helper/urlParameters.js'

const imaxpt = async (formData) => {

    const httpData = {
        
        username            :formData.username,
        password            :formData.password,
        email               :formData.email,
        gender              :formData.gender,
        looking             :formData.seek,
        location            :formData.location,
        dob                 :formData.birth_year + '-' + formData.birth_month + '-' + birth_day,
        click_id            :formData.click_id,
        req_id              :params().offer, // Unsure what is this, need to ask to team
        source_id           :formData.cmp, // Source ID
        key                 :formData.key, // Unique Identifier for Logger
        is_sellable         :!formData.is_sellable,
        lander              :formData.lander,
        device              :formData.device,
        http                :formData.http,
        offer               :"pt",
        city                :formData.location,
        index_id            : "229",
        traf_id             : formData.traf_id,
        t1                  : formData.t1,
        t2                  : formData.t2,
        antifraud           : formData.antifraud,
    }

    return await axios.get(endpoints.imax, { params: httpData })
}

export default imaxpt