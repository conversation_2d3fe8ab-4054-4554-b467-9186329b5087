<script setup>
import { ref } from 'vue'
import Params from '../../assets/js/helper/urlParameters.js'

const isError = ref(false)
const {step, next, inputData, language} = defineProps(['step', 'next', 'inputData', 'language'])


const validateEmail = () => {
    if (/^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/.test(inputData.email.value)) {
        isError.value = false
        next()
    }else{
        isError.value = language.email_error
    }
   
}

</script>

<template>
    
    <div class="card text-center t-bg mx-auto" v-if="step == 3">
        <div class="card-body">
            <div class="mt-auto text-center slides rounded card-black p-2">
                <div class="mb-3">
                    <h5 class="card-title text-pink fs-6 text-uppercase">{{ language.email_text }}</h5>
                </div>
                <div class="input-group">
                    <span class="input-group-text" id="addon-wrapping">
                        <i class="fa-regular fa-at"></i>
                    </span>
                    <input type="email" name="email" class="form-control" aria-label="email" aria-describedby="addon-wrapping" v-model="inputData.email.value">
                </div>
                <div class="col-md-12 p-2 mt-4">
                    <div class="border-danger border-bottom mb-4 p-2 text-danger fs-6" v-if="isError">{{ isError }}</div>
                    <button class="btn next-btn fw-bold text-white text-uppercase" type="button" @click="validateEmail">{{ language.btn_next }}</button>
                </div>
            </div>
        </div>
    </div>        
            
</template>

<style>
</style>