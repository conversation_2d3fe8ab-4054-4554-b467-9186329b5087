<script setup>
import { ref, defineProps } from "vue";
import Steps from "../../lp9/components/views/StepsMarker.vue";
import Language from "../../../assets/js/helper/Language.js";
import Searching from "../../../components/Searching.vue";

const props = defineProps(["steps", "moveNextSlide", "moveBackSlide", "language", "location", "inputs"]);
const steps = ref(props.steps);

const selectGender = (gender) => {
  props.moveNextSlide(gender);
};
</script>

<template>
  <div class="container-fluid d-flex align-items-center justify-content-center min-vh-100">
    <div class="text-center">
      <p class="fs-1 mb-5">{{ Language.guy_girl }}</p>
      <div class="d-flex justify-content-center gap-5 mb-5 button-container">
        <button type="button" class="btn btn-outline-light rounded-5 fs-5" style="height: 5em; width: 16em" @click="selectGender('female')"><img class="me-2" src="../../../assets/icons8-female.png" style="height: 26px" alt="" />{{ Language.girl }}</button>
        <button type="button" class="btn btn-danger rounded-5 fs-5" style="height: 5em; width: 16em" @click="selectGender('male')"><img class="me-2" src="../../../assets/icons8-male.png" style="height: 26px" alt="" />{{ Language.guy }}</button>
      </div>
      <div class="steps d-flex gap-3 justify-content-center mb-3">
        <i class="fa-solid fa-star fs-3 text-white"></i>
        <i class="fa-solid fa-star fs-3 text-white"></i>
        <i class="fa-regular fa-star fs-3 text-white"></i>
        <i class="fa-regular fa-star fs-3 text-white"></i>
        <i class="fa-regular fa-star fs-3 text-white"></i>
        <i class="fa-regular fa-star fs-3 text-white"></i>
        <i class="fa-regular fa-star fs-3 text-white"></i>
      </div>
      <Searching :location="location" :language="Language" />
    </div>
    <Steps :step="steps" />
  </div>
</template>

<style scoped>
@media (max-width: 768px) {
  .fs-1 {
    font-size: 1.5rem;
  }
  .button-container {
    flex-direction: column;
  }
  .btn {
    width: 100%;
    height: 3em !important;
  }
  .steps {
    margin-top: 1em;
  }
}
</style>
