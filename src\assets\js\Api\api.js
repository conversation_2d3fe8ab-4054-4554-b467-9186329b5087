import axios from 'axios'

import Admolly from '../../../assets/js/Api/admolly'
import Advidi from '../../../assets/js/Api/advidi.js'
import AdvidiFr from '../../../assets/js/Api/advidiFr.js'
import Cummission from '../../../assets/js/Api/cummission.js'
import Adsempire from '../../../assets/js/Api/adsempire.js'
import Huge from '../../../assets/js/Api/huge.js'
import Ovation from '../../../assets/js/Api/ovation.js'
import OvationFrOne from '../../../assets/js/Api/ovationFrOne.js'
import OvationFrTwo from '../../../assets/js/Api/ovationFrTwo.js'
import Rebll from '../../../assets/js/Api/rebll.js'
import RebllFr from '../../../assets/js/Api/rebllFr.js'
import Together from '../../../assets/js/Api/together.js'
import TogetherFR from '../../../assets/js/Api/togetherFR.js'
import Hoinew from '../../../assets/js/Api/hoinew.js'
import Wiimax from '../../../assets/js/Api/wiimax.js'
import Ais from '../../../assets/js/Api/ais.js'
import Smartlink from '../../../assets/js/Api/smartlink.js'
import AdmollyAU from "../../../assets/js/Api/admollyAU.js";
import AdmollyCA from "../../../assets/js/Api/admollyCA.js";
import AdsempireCA from '../../../assets/js/Api/adsempireCA.js'
import AdsempireAU from '../../../assets/js/Api/adsempireAU.js'
import AdsempireFR from "../../../assets/js/Api/adsempireFR.js";
import Trafficpartner from "../../../assets/js/Api/trafficpartner.js";
import Mic from "../../../assets/js/Api/mic.js";
import MicDE from "../../../assets/js/Api/micDE.js";
import MicNL from "../../../assets/js/Api/micNL.js";
import MicIT from "../../../assets/js/Api/micIT.js";
import MicAU from "../../../assets/js/Api/micAU.js";
import MicFR from "../../../assets/js/Api/micFR.js";
import AdsempireAT from "../../../assets/js/Api/adsempireAT.js";
import AdsempireDE from "../../../assets/js/Api/adsempireDE.js";
import AdsempireNL from "../../../assets/js/Api/adsempireNL.js";
import AdsempireCH from "../../../assets/js/Api/adsempireCH.js";
import TrafficpartnerAU from "../../../assets/js/Api/trafficpartnerAU.js";
import TrafficpartnerCA from "../../../assets/js/Api/trafficpartnerCA.js";
import TrafficpartnerFR from "../../../assets/js/Api/trafficpartnerFR.js";
import AisAT from "../../../assets/js/Api/aisAT.js";
import AisCH from "../../../assets/js/Api/aisCH.js";
import AisDE from "../../../assets/js/Api/aisDE.js";
import Lovado from '../../../assets/js/Api/lovado.js'
import HoiUS from '../../../assets/js/Api/hoiUS.js'
import MicAT from "../../../assets/js/Api/micAT.js";
import MicCH from "../../../assets/js/Api/micCH.js";
import LovadoAT from "../../../assets/js/Api/lovadoAT.js";
import LovadoCH from "../../../assets/js/Api/lovadoCH.js";
import AdvidiIT from "../../../assets/js/Api/advidiIT.js";
import AdvidiFrTwo from "../../../assets/js/Api/advidiFrTwo.js";
import AdvidiCA from "../../../assets/js/Api/advidiCA.js";
import TrafficpartnerCH from "../../../assets/js/Api/trafficpartnerCH.js";
import TrafficpartnerDE from "../../../assets/js/Api/trafficpartnerDE.js";
import TogetherDE from "../../../assets/js/Api/togetherDE.js";
import OvationIT from "../../../assets/js/Api/OvationIT.js";
import OvationCA from "../../../assets/js/Api/OvationCA.js";
import OvationFR from "../../../assets/js/Api/OvationFR.js";
import TrafficpartnerNL from "../../../assets/js/Api/trafficpartnerNL.js";
import LeadpartnerAT from "../../../assets/js/Api/leadpartnerAT.js";
import LeadpartnerCH from "../../../assets/js/Api/leadpartnerCH.js";
import LeadpartnerDE from "../../../assets/js/Api/leadpartnerDE.js";
import Whitelabels from "../../../assets/js/Api/whitelabels.js";
import WiimaxFR from "../../../assets/js/Api/wiimaxFR.js";
import WiimaxNL from "../../../assets/js/Api/wiimaxNL.js";
import MicCH2 from "../../../assets/js/Api/micCH2.js";
import MicBE from "../../../assets/js/Api/micBE.js";
import AdsempireSE from "../../../assets/js/Api/adsempireSE.js";
import AdsempireJP from "../../../assets/js/Api/adsempireJP.js";
import AdsempireNZ from "../../../assets/js/Api/adsempireNZ.js";
import AdsempireCZ from "../../../assets/js/Api/adsempireCZ.js";
import AdsempireMX from "../../../assets/js/Api/adsempireMX.js";
import WiimaxBE from "../../../assets/js/Api/wiimaxBE.js";
import WiimaxCZ from "../../../assets/js/Api/wiimaxCZ.js";
import WiimaxGR from "../../../assets/js/Api/wiimaxGR.js";
import MicFR2 from "../../../assets/js/Api/micFR2.js";
import MicPT from "../../../assets/js/Api/micPT.js";
import MicPL from "../../../assets/js/Api/micPL.js";
import MicSE from "../../../assets/js/Api/micSE.js";
import MicNO from "../../../assets/js/Api/micNO.js";
import MicDK from "../../../assets/js/Api/micDK.js";
import MicSI from "../../../assets/js/Api/micSI.js";
import MicHU from "../../../assets/js/Api/micHU.js";
import MicGR from "../../../assets/js/Api/micGR.js";
import MicCZ from "../../../assets/js/Api/micCZ.js";
import FlirtyadsAU from "../../../assets/js/Api/flirtyadsAU.js";
import FlirtyadsNZ from "../../../assets/js/Api/flirtyadsNZ.js";
import FlirtyadsIE from "../../../assets/js/Api/flirtyadsIE.js";
import FlirtyadsCA from "../../../assets/js/Api/flirtyadsCA.js";
import FlirtyadsUS from "../../../assets/js/Api/flirtyadsUS.js";
import FlirtyadsIT from "../../../assets/js/Api/flirtyadsIT.js";
import FlirtyadsZA from "../../../assets/js/Api/flirtyadsZA.js";
import FlirtyadsSE from "../../../assets/js/Api/flirtyadsSE.js";
import FlirtyadsDE from "../../../assets/js/Api/flirtyadsDE.js";
import FlirtyadsUK from "../../../assets/js/Api/flirtyadsUK.js";
import FlirtyadsNL from "../../../assets/js/Api/flirtyadsNL.js";
import FlirtyadsFR from "../../../assets/js/Api/flirtyadsFR.js";
import TogetherNZ from "../../../assets/js/Api/togetherNZ.js";
import TogetherMX from "../../../assets/js/Api/togetherMX.js";
import TogetherBR from "../../../assets/js/Api/togetherBR.js";
import TogetherCL from "../../../assets/js/Api/togetherCL.js";
import TogetherAU from "../../../assets/js/Api/togetherAU.js";
import FlirtyadsAUmob from "../../../assets/js/Api/flirtyadsAUmob.js";
import FlirtyadsNZmob from "../../../assets/js/Api/flirtyadsNZmob.js";
import FlirtyadsIEmob from "../../../assets/js/Api/flirtyadsIEmob.js";
import FlirtyadsCAmob from "../../../assets/js/Api/flirtyadsCAmob.js";
import FlirtyadsUSmob from "../../../assets/js/Api/flirtyadsUSmob.js";
import FlirtyadsITmob from "../../../assets/js/Api/flirtyadsITmob.js";
import FlirtyadsZAmob from "../../../assets/js/Api/flirtyadsZAmob.js";
import FlirtyadsSEmob from "../../../assets/js/Api/flirtyadsSEmob.js";
import FlirtyadsDEmob from "../../../assets/js/Api/flirtyadsDEmob.js";
import FlirtyadsUKmob from "../../../assets/js/Api/flirtyadsUKmob.js";
import FlirtyadsNLmob from "../../../assets/js/Api/flirtyadsNLmob.js";
import FlirtyadsFRmob from "../../../assets/js/Api/flirtyadsFRmob.js";
import FlirtyadsUS2 from "../../../assets/js/Api/flirtyadsUS2.js";
import FlirtyadsUSmob2 from "../../../assets/js/Api/flirtyadsUSmob2.js";
import FlirtyadsUS3 from "../../../assets/js/Api/flirtyadsUS3.js";
import FlirtyadsUSmob3 from "../../../assets/js/Api/flirtyadsUSmob3.js";
import FlirtyadsUS4 from "../../../assets/js/Api/flirtyadsUS4.js";
import FlirtyadsUSmob4 from "../../../assets/js/Api/flirtyadsUSmob4.js";
import FlirtyadsUS5 from "../../../assets/js/Api/flirtyadsUS5.js";
import FlirtyadsUSmob5 from "../../../assets/js/Api/flirtyadsUSmob5.js";
import FlirtyadsUS6 from "../../../assets/js/Api/flirtyadsUS6.js";
import FlirtyadsUSmob6 from "../../../assets/js/Api/flirtyadsUSmob6.js";
import FlirtyadsFR2 from "../../../assets/js/Api/flirtyadsFR2.js";
import FlirtyadsFRmob2 from "../../../assets/js/Api/flirtyadsFRmob2.js";
import FlirtyadsFR3 from "../../../assets/js/Api/flirtyadsFR3.js";
import FlirtyadsFRmob3 from "../../../assets/js/Api/flirtyadsFRmob3.js";
import FlirtyadsIT2 from "../../../assets/js/Api/flirtyadsIT.js";
import FlirtyadsITmob2 from "../../../assets/js/Api/flirtyadsITmob.js";
import FlirtyadsIT3 from "../../../assets/js/Api/flirtyadsIT.js";
import FlirtyadsITmob3 from "../../../assets/js/Api/flirtyadsITmob.js";

import config from "../../js/config/config.json"
import Params from '../../../assets/js/helper/urlParameters.js'
import PrepUrl from '../../../assets/js/helper/urlParamPrepare.js'

const apiRequest = async (data, reference, errorMessage) => {

    errorMessage.value = false
    reference.value = true
    let loopCounter = 0
    let tmpError = ''

    let date = new Date(); // object of the date class
    let timestamp = date.getTime(); // To get the timestamp
    data.key = timestamp

    // Waterfall request implementation
    const requests = {
        Admolly: Admolly,
        Adsempire: Adsempire,
        Huge: Huge,
        Ovation: Ovation,
        OvationFrOne: OvationFrOne,
        OvationFrTwo: OvationFrTwo,
        OvationIT: OvationIT,
        OvationCA: OvationCA,
        OvationFR: OvationFR,
        Rebll: Rebll,
        RebllFr: RebllFr,
        TogetherFR: TogetherFR,
        Together: Together,
        Hoinew: Hoinew,
        Wiimax: Wiimax,
        Ais: Ais,
        Advidi: Advidi,
        AdvidiFr: AdvidiFr,
        Cummission: Cummission,
        Smartlink: Smartlink,
        AdmollyAU: AdmollyAU,
        AdmollyCA: AdmollyCA,
        AdsempireCA: AdsempireCA,
        AdsempireAU: AdsempireAU,
        AdsempireFR: AdsempireFR,
        Trafficpartner: Trafficpartner,
        Mic: Mic,
        MicDE: MicDE,
        MicNL: MicNL,
        MicIT: MicIT,
        MicAU: MicAU,
        MicFR: MicFR,
        AdsempireAT: AdsempireAT,
        AdsempireDE: AdsempireDE,
        AdsempireNL: AdsempireNL,
        AdsempireCH: AdsempireCH,
        TrafficpartnerAU: TrafficpartnerAU,
        TrafficpartnerCA: TrafficpartnerCA,
        TrafficpartnerFR: TrafficpartnerFR,
        AisAT: AisAT,
        AisCH: AisCH,
        AisDE: AisDE,
        Lovado: Lovado,
        HoiUS: HoiUS,
        MicAT: MicAT,
        MicCH: MicCH,
        LovadoAT: LovadoAT,
        LovadoCH: LovadoCH,
        AdvidiIT: AdvidiIT,
        AdvidiFrTwo: AdvidiFrTwo,
        AdvidiCA: AdvidiCA,
        TrafficpartnerCH: TrafficpartnerCH,
        TrafficpartnerDE: TrafficpartnerDE,
        TogetherDE: TogetherDE,
        TrafficpartnerNL: TrafficpartnerNL,
        LeadpartnerAT: LeadpartnerAT,
        LeadpartnerCH: LeadpartnerCH,
        LeadpartnerDE: LeadpartnerDE,
        Whitelabels: Whitelabels,
        WiimaxFR: WiimaxFR,
        WiimaxNL: WiimaxNL,
        MicCH2: MicCH2,
        MicBE: MicBE,
        AdsempireSE: AdsempireSE,
        AdsempireJP: AdsempireJP,
        AdsempireNZ: AdsempireNZ,
        AdsempireCZ: AdsempireCZ,
        AdsempireMX: AdsempireMX,
        WiimaxBE: WiimaxBE,
        WiimaxCZ: WiimaxCZ,
        WiimaxGR: WiimaxGR,
        MicFR2: MicFR2,
        MicPT: MicPT,
        MicPL: MicPL,
        MicSE: MicSE,
        MicNO: MicNO,
        MicDK: MicDK,
        MicSI: MicSI,
        MicHU: MicHU,
        MicGR: MicGR,
        MicCZ: MicCZ,
        FlirtyadsAU: FlirtyadsAU,
        FlirtyadsNZ: FlirtyadsNZ,
        FlirtyadsIE: FlirtyadsIE,
        FlirtyadsCA: FlirtyadsCA,
        FlirtyadsUS: FlirtyadsUS,
        FlirtyadsIT: FlirtyadsIT,
        FlirtyadsZA: FlirtyadsZA,
        FlirtyadsSE: FlirtyadsSE,
        FlirtyadsDE: FlirtyadsDE,
        FlirtyadsUK: FlirtyadsUK,
        FlirtyadsNL: FlirtyadsNL,
        FlirtyadsFR: FlirtyadsFR,
        TogetherNZ: TogetherNZ,
        TogetherMX: TogetherMX,
        TogetherBR: TogetherBR,
        TogetherCL: TogetherCL,
        TogetherAU: TogetherAU,
        FlirtyadsAUmob: FlirtyadsAUmob,
        FlirtyadsNZmob: FlirtyadsNZmob,
        FlirtyadsIEmob: FlirtyadsIEmob,
        FlirtyadsCAmob: FlirtyadsCAmob,
        FlirtyadsUSmob: FlirtyadsUSmob,
        FlirtyadsITmob: FlirtyadsITmob,
        FlirtyadsZAmob: FlirtyadsZAmob,
        FlirtyadsSEmob: FlirtyadsSEmob,
        FlirtyadsDEmob: FlirtyadsDEmob,
        FlirtyadsUKmob: FlirtyadsUKmob,
        FlirtyadsNLmob: FlirtyadsNLmob,
        FlirtyadsFRmob: FlirtyadsFRmob,
        FlirtyadsUS2: FlirtyadsUS2,
        FlirtyadsUSmob2: FlirtyadsUSmob2,
        FlirtyadsUS3: FlirtyadsUS3,
        FlirtyadsUSmob3: FlirtyadsUSmob3,
        FlirtyadsUS4: FlirtyadsUS4,
        FlirtyadsUSmob4: FlirtyadsUSmob4,
        FlirtyadsUS5: FlirtyadsUS5,
        FlirtyadsUSmob5: FlirtyadsUSmob5,
        FlirtyadsUS6: FlirtyadsUS6,
        FlirtyadsUSmob6: FlirtyadsUSmob6,
        FlirtyadsFR2: FlirtyadsFR2,
        FlirtyadsFRmob2: FlirtyadsFRmob2,
        FlirtyadsFR3: FlirtyadsFR3,
        FlirtyadsFRmob3: FlirtyadsFRmob3,
        FlirtyadsIT2: FlirtyadsIT2,
        FlirtyadsITmob2: FlirtyadsITmob2,
        FlirtyadsIT3: FlirtyadsIT3,
        FlirtyadsITmob3: FlirtyadsITmob3,
    };

    const selected = Params().select

    if(selected){
        // Loop through selected services
        for(let x in selected){

            if(selected[x] in requests){
                const apiRequest = await requests[selected[x]](data)
                if(apiRequest.data.code == 200){
                  tmpError = false;
                  let redirect = apiRequest.data.url;
                  if (apiRequest.data.source == "Smartlink") {
                    redirect = redirect + Params().url;
                  }

                  window.location = redirect
                  return false

                }else{
                    tmpError = apiRequest.data.message
                }
            }

            if( loopCounter == selected.length -1 ){
                errorMessage.value = tmpError
            }
            loopCounter++
        }
    }else{

        /**
         * Initiate Waterfall call approach
         */
        for(let seq in requests){
            const apiRequest = await requests[seq](data)
            if(apiRequest.data.code == 200){
                tmpError = false
                window.location = PrepUrl(apiRequest.data.url, Params().url)
                break
            }else{
                tmpError = apiRequest.data.message
            }

            if(loopCounter == requests.length - 1){
                errorMessage.value = tmpError
            }
            loopCounter++
        }

    }

    reference.value = false
}

export default apiRequest