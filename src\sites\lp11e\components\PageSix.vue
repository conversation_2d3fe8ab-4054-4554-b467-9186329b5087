<script setup>
import { ref, defineProps, onMounted } from "vue";
import Steps from "../../lp9/components/views/StepsMarker.vue";
import Language from "../../../assets/js/helper/Language.js";
import Swal from "sweetalert2";
import Searching from "../../../components/Searching.vue";

const props = defineProps({
  steps: Number,
  moveNextSlide: Function,
  moveBackSlide: Function,
  language: Object,
  location: String,
  inputs: Object,
  emailValidated: Boolean,
});

const steps = ref(props.steps);
const validEmail = ref(false);

onMounted(() => {
  const urlParams = new URLSearchParams(window.location.search);
  const emailFromParams = urlParams.get("email");

  if (emailFromParams) {
    props.inputs.email.value = emailFromParams;
    setTimeout(() => {
      const emailInput = document.getElementById("email");
      if (emailInput) {
        emailInput.value = emailFromParams;
      }
    }, 0);
  }
});

const validateEmail = () => {
  const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  const isValid = emailPattern.test(props.inputs.email.value);
  validEmail.value = isValid;
  props.inputs.email.valid = isValid;

  // If email is valid, move to next step immediately
  if (isValid) {
    props.moveNextSlide();
  }
  return isValid;
};

const continueToNextSlide = () => {
  if (!validateEmail()) {
    const alertMessage = document.getElementById("alert-message").innerText;
    Swal.fire({
      text: alertMessage,
      customClass: {
        popup: "custom-swal-popup",
      },
    });
  }
};
</script>

<template>
  <div class="container-fluid d-flex align-items-center justify-content-center min-vh-100">
    <div class="text-center">
      <p class="fs-1 mb-5">{{ Language.email_text }}</p>
      <div class="d-flex justify-content-center gap-5 mb-5 button-container">
        <input v-model="props.inputs.email.value" @blur="validateEmail" @keyup.enter="continueToNextSlide" class="form-control rounded-5" style="width: 25em" type="email" id="email" placeholder="<EMAIL>" @keypress="handleKeyPress" />
        <button type="button" class="btn btn-danger rounded-5 fs-5" style="height: 5em; width: 21em" @click="continueToNextSlide">{{ Language.continue }}</button>
      </div>
      <div class="text-center mt-2">
        <p class="text-dark" style="font-size: 10px">{{ Language.spam_alert }}</p>
      </div>
      <div class="steps d-flex gap-3 justify-content-center mb-3">
        <i class="fa-solid fa-star fs-3 text-white"></i>
        <i class="fa-solid fa-star fs-3 text-white"></i>
        <i class="fa-solid fa-star fs-3 text-white"></i>
        <i class="fa-solid fa-star fs-3 text-white"></i>
        <i class="fa-solid fa-star fs-3 text-white"></i>
        <i class="fa-solid fa-star fs-3 text-white"></i>
        <i class="fa-regular fa-star fs-3 text-white"></i>
      </div>
      <Searching :location="location" :language="Language" />
    </div>
    <div id="alert-message" style="display: none">
      {{ Language.email_error }}
    </div>
    <Steps :step="steps" />
  </div>
</template>

<style scoped>
@media (max-width: 768px) {
  .fs-1 {
    font-size: 1.5rem;
  }
  .button-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
  .form-control {
    width: 20em !important;
    height: 4em;
  }
  .btn {
    width: 16em !important;
    height: 3em !important;
  }
  .steps {
    margin-top: 1em;
  }
}
</style>
