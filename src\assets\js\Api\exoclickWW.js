import axios from 'axios'
import endpoints from '../config/endpoints.json'
import params from '../helper/urlParameters.js'

const exoclick = async (formData) => {

    let httpData = {
        username        : formData.username,
        password        : formData.password,
        email           : formData.email,
        gender          : formData.gender,
        dob             : formData.birth_year + "-" + formData.birth_month + "-" + formData.birth_day,
        key             : formData.key,
        click_id        : formData.click_id,
        source_id       : formData.source_id,
        is_sellable     : !formData.is_sellable,
        device          : formData.device,
        lander          : formData.lander,
        http            : formData.http,
        offer           : 'WW',
        language        : formData.locale,
        t1              : formData.t1,
        t2              : formData.t2,
        looking         : formData.seek,
        img             : formData.image,
        city            : formData.location,
        index_id        : "221",
        traf_id         : formData.traf_id,
        antifraud   : formData.antifraud,
    }

    return await axios.get(endpoints.exoclick, { params: httpData })

}

export default exoclick