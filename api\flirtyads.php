<?php
include_once('./log.php');

$config = file_exists('./config/api.json') ? json_decode(file_get_contents('./config/api.json')) : null;

/**
 * Make sure the configuration is not missing or empty
 */
if($config == null){
	$fileMissing = json_encode([
		'code' => 401,
		'message' => 'Api configuration file missing',
	]);
	die($fileMissing);
}

$offer = $_GET['offer'];
$startTime	= microtime(true);

$ch = curl_init($config->flirtyads->$offer->tracking);
curl_setopt_array($ch, array(
	CURLOPT_RETURNTRANSFER => true,
	CURLOPT_FOLLOWLOCATION => false,
	CURLOPT_SSL_VERIFYPEER => false
));
$getTransactionId = curl_exec($ch);

$url = preg_match('/<a href="(.+)">/', $getTransactionId, $match);
$url = parse_url($match[1]);
$url = parse_str($url['query'], $newUrl);
$transaction_id = $newUrl['transaction_id'];

$data = [
	"transaction_id"    		=> $transaction_id,
	"affiliate_id"				=> 1,
	'offer_id'					=> $config->flirtyads->$offer->offer_id,
	'custom_stransaction_id'	=> $_GET['click_id'],
	'sub1'						=> $_GET['click_id'],
	"landing_page"				=> 'https://hotdates.us',
	"email"						=> $_GET['email'],
	"username"					=> $_GET['username'],
	"password"					=> $_GET['password'],
	"date_of_birth"				=> $_GET['dob'],
	'terms_accepted'			=> true,
	'gender'					=> $_GET['gender'],
	"city"						=> $_GET['city'],
	"region"					=> $_GET['country'],
];

$ch = curl_init($config->flirtyads->$offer->endpoint);
curl_setopt_array($ch, array(
	CURLOPT_POST	=> true,
	CURLOPT_RETURNTRANSFER => true,
	CURLOPT_FOLLOWLOCATION => true,
	CURLOPT_SSL_VERIFYPEER => false,
	CURLOPT_HTTPHEADER => array(
		'Content-Type: application/json',
		'Accept: application/json',
		'Authorization: Bearer '. $config->flirtyads->$offer->token
	),
	CURLOPT_POSTFIELDS => json_encode($data)
));

$response = curl_exec($ch);
$responseData = json_decode($response, true);
$httpcode = curl_getinfo($ch, CURLINFO_RESPONSE_CODE);
curl_close($ch);

// Do not include Email in data storage
$data['email'] = '************';

$return = [
	'code' => 200,
	'message' => '',
	'url' => '',
	'time' => '0',
	'request_data' => $data,
	'response_data' => '',
	'source' => 'Flirtyads ' . $offer,
	'offer' => $offer
];

/**
 * 1. Check if user created successfully - build success response
 * 2. If user creation failed - build error response
 */
if($response != false){
	if(isset($responseData['errors'])){
		$return['code'] = $httpcode;
		foreach($responseData['errors'] as $key => $val){
			$return['message'] = $responseData['errors'][$key][0];
		}
	}else if(isset($responseData['complete_registration_url'])){
		$return['message'] = 'Account successfully created';
		$return['url'] = @$responseData['complete_registration_url'];
	}
}else{
	$responseData = 'Unable to connect API';
}

$endTime = microtime(true);
$return['key']				= $_GET['key'];
$return['response_data']	= $responseData ? $responseData : $response;
$return['start_time']		= $startTime;
$return['end_time']			= $endTime;
$return['endpoint']			= $config->flirtyads->$offer->endpoint;

$return['time'] 			= number_format( ( $endTime - $startTime ), 2);
$return['click_id']			= $_GET['click_id'];
$return['source_id']		= $_GET['source_id'];
$return['is_sellable']		= $_GET['is_sellable'];

$return['email']			= $_GET['email'];
$return['username']			= $_GET['username'];
$return['password']			= $_GET['password'];
$return['gender']			= $_GET['gender'];
$return['looking']			= $_GET['looking'];
$return['dob']				= $_GET['dob'];

$return['city']				= $_GET['city'];
$return['lander']			= $_GET['lander'];
$return['device']			= $_GET['device'];
$return['http']				= $_GET['http'];
$return['index_id']			= $_GET['index_id'];
$return['antifraud']		= isset($_GET['antifraud']) ? json_encode($_GET['antifraud']) : '';

// Change the redirection url
if(!empty($return['url'])){
	$redirectionUrl = urldecode($return['url']);
	$redirectionUrl = $redirectionUrl.'&sub1='.$_GET['click_id'].'&sub2='.$_GET['subid1'].'&sub3='.$_GET['subid2'];
	$return['url'] = $redirectionUrl;
}


// Send Data to Log API
$log	= new Log($return);
$send	= $log->send($config->log->endpoint);

echo json_encode($return);