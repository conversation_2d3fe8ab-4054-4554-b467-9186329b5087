<?php
include_once('./log.php');

$config = file_exists('./config/api.json') ? json_decode(file_get_contents('./config/api.json')) : null;

/**
 * Make sure the configuration is not missing or empty
 */
if($config == null){
	$fileMissing = json_encode([
		'code' => 401,
		'message' => 'Api configuration file missing',
	]);
	die($fileMissing);
}

$startTime = microtime(true);

$hashed = isset($_GET['traf_id']) ? substr(md5($_GET['traf_id']), 0, 16) : "";

$data = [
    'email' => $_GET['email'],
    'data' => [
        'group_id'		=> $config->trafficmansion->group_id,
        'ipv4'			=> $_GET['device']['ip'],
		'subid'			=> $_GET['click_id'],
		'keyword'		=> $hashed
    ]
];

$params = [
    'arguments' => json_encode($data),
    'client' => $config->trafficmansion->username,
    'signature' => hash_hmac('sha1', json_encode($data), $config->trafficmansion->password),
];

$url = $config->trafficmansion->endpoint . '?' . http_build_query($params);
$curl = curl_init();
curl_setopt_array($curl, array(
    CURLOPT_RETURNTRANSFER	=> true,
    CURLOPT_FOLLOWLOCATION	=> true,
	CURLOPT_SSL_VERIFYHOST	=> false,
	CURLOPT_SSL_VERIFYPEER	=> false,
    CURLOPT_MAXREDIRS		=> 5,
));
curl_setopt($curl, CURLOPT_URL, $url);
$response = curl_exec($curl);
curl_close($curl);

$responseData = json_decode($response, true);

// Do not include Email in data storage
$data['email'] = '************';

$return = [
	'code' => 200,
	'message' => '',
	'url' => '',
	'time' => '0',
	'request_data' => $data,
	'response_data' => '',
	'source' => 'Trafficmansion - ' . $_GET['offer']];


/**
 * 1. Check if user created successfully - build success response
 * 2. If user creation failed - build error response
 */
if($response != false){
	if(isset($responseData['status']) && $responseData['status'] == 'SUCCESS' && isset($responseData['url'])){
		$return['message'] = $responseData['status'];
		$return['code'] = 200;
		$return['url'] = $responseData['url'];
	}
	else{
		$return['message'] = isset($responseData['status']) && isset($responseData['error']) ? $responseData['status'] . ' - ' . $responseData['error'] : 'Error';
		$return['code'] = 400;
	}
}else{
	$responseData = 'Unable to connect API';
}

$endTime = microtime(true);
$return['key']				= $_GET['key'];
$return['response_data']	= $responseData;
$return['start_time']		= $startTime;
$return['end_time']			= $endTime;
$return['endpoint']			= $config->trafficmansion->endpoint;
$return['time'] 			= number_format( ( $endTime - $startTime ), 2);
$return['click_id']			= $_GET['click_id'];
$return['source_id']		= $_GET['source_id'];
$return['is_sellable']		= $_GET['is_sellable'];

$return['email']			= $_GET['email'];
$return['username']			= $_GET['username'];
$return['password']			= $_GET['password'];
$return['gender']			= $_GET['gender'];
$return['looking']			= $_GET['looking'];
$return['dob']				= $_GET['birth_year'].'-'.$_GET['birth_month'].'-'.$_GET['birth_day'];

$return['device']			= $_GET['device'];
$return['city']				= $_GET['device']['geo']['city'];
$return['lander']			= $_GET['lander'];
$return['http']				= $_GET['http'];
$return['index_id']			= $_GET['index_id'];
$return['antifraud']		= isset($_GET['antifraud']) ? json_encode($_GET['antifraud']) : '';

// Send Data to Log API
$log	= new Log($return);
$log->send($config->log->endpoint);

echo json_encode($return);