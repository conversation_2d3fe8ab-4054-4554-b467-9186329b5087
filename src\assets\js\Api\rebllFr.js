import axios from 'axios'
import endpoints from '../../../assets/js/config/endpoints.json'
import params from '../../../assets/js/helper/urlParameters.js'

const rebll = async (formData) => {

    const httpData = {
        email           : formData.email,
        gender          : formData.gender,
        username        : formData.username,
        password        : formData.password,
        dob             : formData.birth_year+'-'+formData.birth_month+'-'+formData.birth_day,
        t1_t2           : formData.cmp,
        click_id        : formData.click_id,
        key             : formData.key,
        is_sellable     : !formData.is_sellable,
        device          : formData.device,
        lander          : formData.lander,
        http            : formData.http,
        offer           : 'fr',
        looking         : formData.seek,
        city            : formData.location,
        index_id        : "13",
        traf_id         : formData.traf_id,
        source_id       : formData.source_id,
        antifraud   : formData.antifraud,
    }

    console.log(httpData)

    return await axios.get(endpoints.rebll, { params: httpData })
}

export default rebll