<script>
export default {
    name    : 'steps-marker',
    props   : ['step']
}
</script>

<template>
    <div class="d-flex justify-content-center mt-3 d-none">
        <div class="form-check">
            <input class="form-check-input custom-radio" type="radio" value="option1" :checked="step >= 1" disabled>
        </div>
        <div class="form-check">
            <input class="form-check-input custom-radio" type="radio" value="option2" :checked="step >= 2" disabled>
        </div>
        <div class="form-check">
            <input class="form-check-input custom-radio" type="radio" value="option3" :checked="step >= 3" disabled>
        </div>
        <div class="form-check">
            <input class="form-check-input custom-radio" type="radio" value="option4" :checked="step >= 4" disabled>
        </div>
        <div class="form-check">
            <input class="form-check-input custom-radio" type="radio" value="option5" :checked="step >= 5" disabled>
        </div>
        <div class="form-check">
            <input class="form-check-input custom-radio" type="radio" value="option6" :checked="step >= 6" disabled>
        </div>
        <div class="form-check">
            <input class="form-check-input custom-radio" type="radio" value="option7" :checked="step >= 7" disabled>
        </div>
    </div>
</template>