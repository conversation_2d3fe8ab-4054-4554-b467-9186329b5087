import axios from "axios";

import Admolly from "../../../assets/js/Api/admolly";
import Advidi from "../../../assets/js/Api/advidi.js";
import AdvidiFr from "../../../assets/js/Api/advidiFr.js";
import Cummission from "../../../assets/js/Api/cummission.js";
import Adsempire from "../../../assets/js/Api/adsempire.js";
import Huge from "../../../assets/js/Api/huge.js";
import Ovation from "../../../assets/js/Api/ovation.js";
import OvationFrOne from "../../../assets/js/Api/ovationFrOne.js";
import OvationFrTwo from "../../../assets/js/Api/ovationFrTwo.js";
import Rebll from "../../../assets/js/Api/rebll.js";
import RebllFr from "../../../assets/js/Api/rebllFr.js";
import Together from "../../../assets/js/Api/together.js";
import TogetherFR from "../../../assets/js/Api/togetherFR.js";
import Hoinew from "../../../assets/js/Api/hoinew.js";
import Wiimax from "../../../assets/js/Api/wiimax.js";
import Ais from "../../../assets/js/Api/ais.js";
import Smartlink from "../../../assets/js/Api/smartlink.js";
import AdmollyAU from "../../../assets/js/Api/admollyAU.js";
import AdmollyCA from "../../../assets/js/Api/admollyCA.js";
import AdsempireCA from "../../../assets/js/Api/adsempireCA.js";
import AdsempireAU from "../../../assets/js/Api/adsempireAU.js";
import AdsempireFR from "../../../assets/js/Api/adsempireFR.js";
import Trafficpartner from "../../../assets/js/Api/trafficpartner.js";
import Mic from "../../../assets/js/Api/mic.js";
import MicDE from "../../../assets/js/Api/micDE.js";
import MicNL from "../../../assets/js/Api/micNL.js";
import MicIT from "../../../assets/js/Api/micIT.js";
import MicAU from "../../../assets/js/Api/micAU.js";
import MicFR from "../../../assets/js/Api/micFR.js";
import AdsempireAT from "../../../assets/js/Api/adsempireAT.js";
import AdsempireDE from "../../../assets/js/Api/adsempireDE.js";
import AdsempireNL from "../../../assets/js/Api/adsempireNL.js";
import AdsempireCH from "../../../assets/js/Api/adsempireCH.js";
import TrafficpartnerAU from "../../../assets/js/Api/trafficpartnerAU.js";
import TrafficpartnerCA from "../../../assets/js/Api/trafficpartnerCA.js";
import TrafficpartnerFR from "../../../assets/js/Api/trafficpartnerFR.js";
import AisAT from "../../../assets/js/Api/aisAT.js";
import AisCH from "../../../assets/js/Api/aisCH.js";
import AisDE from "../../../assets/js/Api/aisDE.js";
import Lovado from "../../../assets/js/Api/lovado.js";
import HoiUS from "../../../assets/js/Api/hoiUS.js";
import MicAT from "../../../assets/js/Api/micAT.js";
import MicCH from "../../../assets/js/Api/micCH.js";
import LovadoAT from "../../../assets/js/Api/lovadoAT.js";
import LovadoCH from "../../../assets/js/Api/lovadoCH.js";
import AdvidiIT from "../../../assets/js/Api/advidiIT.js";
import AdvidiFrTwo from "../../../assets/js/Api/advidiFrTwo.js";
import AdvidiCA from "../../../assets/js/Api/advidiCA.js";
import TrafficpartnerCH from "../../../assets/js/Api/trafficpartnerCH.js";
import TrafficpartnerDE from "../../../assets/js/Api/trafficpartnerDE.js";
import TogetherDE from "../../../assets/js/Api/togetherDE.js";
import OvationIT from "../../../assets/js/Api/OvationIT.js";
import OvationCA from "../../../assets/js/Api/OvationCA.js";
import OvationFR from "../../../assets/js/Api/OvationFR.js";
import TrafficpartnerNL from "../../../assets/js/Api/trafficpartnerNL.js";
import LeadpartnerAT from "../../../assets/js/Api/leadpartnerAT.js";
import LeadpartnerCH from "../../../assets/js/Api/leadpartnerCH.js";
import LeadpartnerDE from "../../../assets/js/Api/leadpartnerDE.js";
import Whitelabels from "../../../assets/js/Api/whitelabels.js";
import WiimaxFR from "../../../assets/js/Api/wiimaxFR.js";
import WiimaxNL from "../../../assets/js/Api/wiimaxNL.js";
import MicCH2 from "../../../assets/js/Api/micCH2.js";
import MicBE from "../../../assets/js/Api/micBE.js";
import AdsempireSE from "../../../assets/js/Api/adsempireSE.js";
import AdsempireJP from "../../../assets/js/Api/adsempireJP.js";
import AdsempireNZ from "../../../assets/js/Api/adsempireNZ.js";
import AdsempireCZ from "../../../assets/js/Api/adsempireCZ.js";
import AdsempireMX from "../../../assets/js/Api/adsempireMX.js";
import WiimaxBE from "../../../assets/js/Api/wiimaxBE.js";
import WiimaxCZ from "../../../assets/js/Api/wiimaxCZ.js";
import WiimaxGR from "../../../assets/js/Api/wiimaxGR.js";
import MicFR2 from "../../../assets/js/Api/micFR2.js";
import MicPT from "../../../assets/js/Api/micPT.js";
import MicPL from "../../../assets/js/Api/micPL.js";
import MicSE from "../../../assets/js/Api/micSE.js";
import MicNO from "../../../assets/js/Api/micNO.js";
import MicDK from "../../../assets/js/Api/micDK.js";
import MicSI from "../../../assets/js/Api/micSI.js";
import MicHU from "../../../assets/js/Api/micHU.js";
import MicGR from "../../../assets/js/Api/micGR.js";
import MicCZ from "../../../assets/js/Api/micCZ.js";
import FlirtyadsAU from "../../../assets/js/Api/flirtyadsAU.js";
import FlirtyadsNZ from "../../../assets/js/Api/flirtyadsNZ.js";
import FlirtyadsIE from "../../../assets/js/Api/flirtyadsIE.js";
import FlirtyadsCA from "../../../assets/js/Api/flirtyadsCA.js";
import FlirtyadsUS from "../../../assets/js/Api/flirtyadsUS.js";
import FlirtyadsIT from "../../../assets/js/Api/flirtyadsIT.js";
import FlirtyadsZA from "../../../assets/js/Api/flirtyadsZA.js";
import FlirtyadsSE from "../../../assets/js/Api/flirtyadsSE.js";
import FlirtyadsDE from "../../../assets/js/Api/flirtyadsDE.js";
import FlirtyadsUK from "../../../assets/js/Api/flirtyadsUK.js";
import FlirtyadsNL from "../../../assets/js/Api/flirtyadsNL.js";
import FlirtyadsFR from "../../../assets/js/Api/flirtyadsFR.js";
import TogetherNZ from "../../../assets/js/Api/togetherNZ.js";
import TogetherMX from "../../../assets/js/Api/togetherMX.js";
import TogetherBR from "../../../assets/js/Api/togetherBR.js";
import TogetherCL from "../../../assets/js/Api/togetherCL.js";
import TogetherAU from "../../../assets/js/Api/togetherAU.js";
import FlirtyadsAUmob from "../../../assets/js/Api/flirtyadsAUmob.js";
import FlirtyadsNZmob from "../../../assets/js/Api/flirtyadsNZmob.js";
import FlirtyadsIEmob from "../../../assets/js/Api/flirtyadsIEmob.js";
import FlirtyadsCAmob from "../../../assets/js/Api/flirtyadsCAmob.js";
import FlirtyadsUSmob from "../../../assets/js/Api/flirtyadsUSmob.js";
import FlirtyadsITmob from "../../../assets/js/Api/flirtyadsITmob.js";
import FlirtyadsZAmob from "../../../assets/js/Api/flirtyadsZAmob.js";
import FlirtyadsSEmob from "../../../assets/js/Api/flirtyadsSEmob.js";
import FlirtyadsDEmob from "../../../assets/js/Api/flirtyadsDEmob.js";
import FlirtyadsUKmob from "../../../assets/js/Api/flirtyadsUKmob.js";
import FlirtyadsNLmob from "../../../assets/js/Api/flirtyadsNLmob.js";
import FlirtyadsFRmob from "../../../assets/js/Api/flirtyadsFRmob.js";
import FlirtyadsUS2 from "../../../assets/js/Api/flirtyadsUS2.js";
import FlirtyadsUSmob2 from "../../../assets/js/Api/flirtyadsUSmob2.js";
import FlirtyadsUS3 from "../../../assets/js/Api/flirtyadsUS3.js";
import FlirtyadsUSmob3 from "../../../assets/js/Api/flirtyadsUSmob3.js";
import FlirtyadsUS4 from "../../../assets/js/Api/flirtyadsUS4.js";
import FlirtyadsUSmob4 from "../../../assets/js/Api/flirtyadsUSmob4.js";
import FlirtyadsUS5 from "../../../assets/js/Api/flirtyadsUS5.js";
import FlirtyadsUSmob5 from "../../../assets/js/Api/flirtyadsUSmob5.js";
import FlirtyadsUS6 from "../../../assets/js/Api/flirtyadsUS6.js";
import FlirtyadsUSmob6 from "../../../assets/js/Api/flirtyadsUSmob6.js";
import FlirtyadsFR2 from "../../../assets/js/Api/flirtyadsFR2.js";
import FlirtyadsFRmob2 from "../../../assets/js/Api/flirtyadsFRmob2.js";
import FlirtyadsFR3 from "../../../assets/js/Api/flirtyadsFR3.js";
import FlirtyadsFRmob3 from "../../../assets/js/Api/flirtyadsFRmob3.js";
import FlirtyadsIT2 from "../../../assets/js/Api/flirtyadsIT2.js";
import FlirtyadsITmob2 from "../../../assets/js/Api/flirtyadsITmob2.js";
import FlirtyadsIT3 from "../../../assets/js/Api/flirtyadsIT3.js";
import FlirtyadsITmob3 from "../../../assets/js/Api/flirtyadsITmob3.js";
import FlirtyadsAU2 from "../../../assets/js/Api/flirtyadsAU.js";
import FlirtyadsAUmob2 from "../../../assets/js/Api/flirtyadsAUmob.js";
import FlirtyadsES from "../../../assets/js/Api/flirtyadsES.js";
import FlirtyadsESmob from "../../../assets/js/Api/flirtyadsESmob.js";
import FlirtyadsES2 from "../../../assets/js/Api/flirtyadsES2.js";
import FlirtyadsESmob2 from "../../../assets/js/Api/flirtyadsESmob2.js";
import SevenClicksAU from "../../../assets/js/Api/sevenclicksAU.js";
import SevenClicksCA from "../../../assets/js/Api/sevenclicksCA.js";
import SevenClicksGB from "../../../assets/js/Api/sevenclicksGB.js";
import SevenClicksIE from "../../../assets/js/Api/sevenclicksIE.js";
import SevenClicksNZ from "../../../assets/js/Api/sevenclicksNZ.js";
import SevenClicksUS from "../../../assets/js/Api/sevenclicksUS.js";
import SevenClicksAT from "../../../assets/js/Api/sevenclicksAT.js";
import SevenClicksCH from "../../../assets/js/Api/sevenclicksCH.js";
import SevenClicksDE from "../../../assets/js/Api/sevenclicksDE.js";
import FlirtyadsPT from "../../../assets/js/Api/flirtyadsPT.js";
import FlirtyadsPTmob from "../../../assets/js/Api/flirtyadsPTmob.js";
import DatingstarsAU from "../../../assets/js/Api/datingstarsAU.js";
import DatingstarsBE from "../../../assets/js/Api/datingstarsBE.js";
import DatingstarsNL from "../../../assets/js/Api/datingstarsNL.js";
import DatingleadsAU from "../../../assets/js/Api/datingleadsAU.js";
import DatingleadsBE from "../../../assets/js/Api/datingleadsBE.js";
import DatingleadsNL from "../../../assets/js/Api/datingleadsNL.js";
import DatingleadsUS from "../../../assets/js/Api/datingleadsUS.js";
import DatingleadsDE from "../../../assets/js/Api/datingleadsDE.js";
import MireliaUS from "../../../assets/js/Api/mireliaUS.js";
import MireliaUK from "../../../assets/js/Api/mireliaUK.js";
import MireliaCA from "../../../assets/js/Api/mireliaCA.js";
import MireliaAU from "../../../assets/js/Api/mireliaAU.js";
import MireliaNZ from "../../../assets/js/Api/mireliaNZ.js";
import MireliaAT from "../../../assets/js/Api/mireliaAT.js";
import MireliaFR from "../../../assets/js/Api/mireliaFR.js";
import MireliaDE from "../../../assets/js/Api/mireliaDE.js";
import MireliaCH from "../../../assets/js/Api/mireliaCH.js";
import AdsempireUK from "../../../assets/js/Api/adsempireUK.js";
import SevenClicksNO from "../../../assets/js/Api/sevenclicksNO.js";
import SevenClicksSE from "../../../assets/js/Api/sevenclicksSE.js";
import SevenClicksDK from "../../../assets/js/Api/sevenclicksDK.js";
import MicUK from "../../../assets/js/Api/micUK.js";
import DatingPartnersUS from "../../../assets/js/Api/datingpartnersUS.js";
import DatingPartnersUK from "../../../assets/js/Api/datingpartnersUK.js";
import DatingPartnersCA from "../../../assets/js/Api/datingpartnersCA.js";
import ViceROIUS from "../../../assets/js/Api/viceroiUS.js";
import ViceROICZ from "../../../assets/js/Api/viceroiCZ.js";
import ViceROICO from "../../../assets/js/Api/viceroiCO.js";
import ViceROIDE from "../../../assets/js/Api/viceroiDE.js";
import MoarUS from "../../../assets/js/Api/moarUS.js";
import Affmy from "../../../assets/js/Api/affmy.js";
import MoarWellHelloUS from "../../../assets/js/Api/moarWellHelloUS.js";
import MoarUK from "../../../assets/js/Api/moarUK.js";
import MoarCA from "../../../assets/js/Api/moarCA.js";
import DatingleadsCH from "../../../assets/js/Api/datingleadsCH.js";
import DatingleadsAT from "../../../assets/js/Api/datingleadsAT.js";
import DatingleadsUK from "../../../assets/js/Api/datingleadsUK.js";
import DatingleadsCA from "../../../assets/js/Api/datingleadsCA.js";
import DatingleadsNZ from "../../../assets/js/Api/datingleadsNZ.js";
import DatingleadsFR from "../../../assets/js/Api/datingleadsFR.js";
import DatingleadsES from "../../../assets/js/Api/datingleadsES.js";
import DatingleadsIT from "../../../assets/js/Api/datingleadsIT.js";
import DatingleadsIE from "../../../assets/js/Api/datingleadsIE.js";
import DatingleadsPL from "../../../assets/js/Api/datingleadsPL.js";
import DatingleadsPT from "../../../assets/js/Api/datingleadsPT.js";
import DatingleadsGR from "../../../assets/js/Api/datingleadsGR.js";
import DatingleadsTR from "../../../assets/js/Api/datingleadsTR.js";
import DatingleadsCZ from "../../../assets/js/Api/datingleadsCZ.js";
import DatingleadsFI from "../../../assets/js/Api/datingleadsFI.js";
import DatingleadsSE from "../../../assets/js/Api/datingleadsSE.js";
import DatingleadsDK from "../../../assets/js/Api/datingleadsDK.js";
import DatingleadsNO from "../../../assets/js/Api/datingleadsNO.js";
import DatingleadsIL from "../../../assets/js/Api/datingleadsIL.js";
import DatingleadsRO from "../../../assets/js/Api/datingleadsRO.js";
import DatingleadsRS from "../../../assets/js/Api/datingleadsRS.js";
import RebllIT from "../../../assets/js/Api/rebllIT.js";
import RebllUK from "../../../assets/js/Api/rebllUK.js";
import RebllUK2 from "../../../assets/js/Api/rebllUK2.js";
import RebllUS2 from "../../../assets/js/Api/rebllUS2.js";
import RebllUS3 from "../../../assets/js/Api/rebllUS3.js";
import FlirtKingsUK from "../../../assets/js/Api/flirtkingsUK.js";
import FlirtKingsCA from "../../../assets/js/Api/flirtkingsCA.js";
import FlirtKingsAU from "../../../assets/js/Api/flirtkingsAU.js";
import HoiDK from "../../../assets/js/Api/hoiDK.js";
import WiimaxUK from "../../../assets/js/Api/wiimaxUK.js";
import WiimaxAU from "../../../assets/js/Api/wiimaxAU.js";
import AdveryUK from "../../../assets/js/Api/adveryUK.js";
import AdveryUS from "../../../assets/js/Api/adveryUS.js";
import AdveryCA from "../../../assets/js/Api/adveryCA.js";
import Loudbids from "../../../assets/js/Api/loudbids.js";
import WiimaxDE from "../../../assets/js/Api/wiimaxDE.js";
import WiimaxAT from "../../../assets/js/Api/wiimaxAT.js";
import WiimaxCH from "../../../assets/js/Api/wiimaxCH.js";
import AdveryAU from "../../../assets/js/Api/adveryAU.js";
import AdveryFR from "../../../assets/js/Api/adveryFR.js";
import ExoClickDACH from "../../../assets/js/Api/exoclickDACH.js";
import ExoClickFR from "../../../assets/js/Api/exoclickFR.js";
import ExoClickUS from "../../../assets/js/Api/exoclickUS.js";
import ExoClickWW from "../../../assets/js/Api/exoclickWW.js";
import OvationUK from "../../../assets/js/Api/OvationUK.js";
import ImaxDE from "../../../assets/js/Api/imaxDE.js";
import ImaxIT from "../../../assets/js/Api/imaxIT.js";
import ImaxNO from "../../../assets/js/Api/imaxNO.js";
import ImaxPL from "../../../assets/js/Api/imaxPL.js";
import ImaxPT from "../../../assets/js/Api/imaxPT.js";
import DatingleadsFbIT from "../../../assets/js/Api/datingleadsFbIT.js";
import DatingleadsFbNL from "../../../assets/js/Api/datingleadsFbNL.js";
import DatingleadsFbBE from "../../../assets/js/Api/datingleadsFbBE.js";
import TrafficcompannyNL from "../../../assets/js/Api/trafficcompanyNL.js";
import TrafficcompannyFR from "../../../assets/js/Api/trafficcompanyFR.js";
import TrafficcompannyDE from "../../../assets/js/Api/trafficcompanyDE.js";
import TrafficcompannyIT from "../../../assets/js/Api/trafficcompanyIT.js";
import SalamandraFR from "../../../assets/js/Api/salamandraFR.js";
import HoiUsFB from "../../../assets/js/Api/hoiusfb.js";
import EmediabuildersUS from "../Api/emediabuildersUS.js";
import EmediabuildersCA from "../Api/emediabuildersCA.js";
import EmediabuildersAU from "../Api/emediabuildersAU.js";
import EmediabuildersNZ from "../Api/emediabuildersNZ.js";
import EmediabuildersNL from "../Api/emediabuildersNL.js";
import EmediabuildersDK from "../Api/emediabuildersDK.js";
import EmediabuildersDE from "../Api/emediabuildersDE.js";
import EmediabuildersGB from "../Api/emediabuildersGB.js";
import TrafficMansionUS from "../Api/trafficMansionUS.js";
import TrafficMansionCA from "../Api/trafficMansionCA.js";
import TrafficMansionAU from "../Api/trafficMansionAU.js";
import TrafficMansionGB from "../Api/trafficMansionGB.js";
import TrafficMansionIE from "../Api/trafficMansionIE.js";
import TrafficMansionNZ from "../Api/trafficMansionNZ.js";
import RTB from "../../../assets/js/Api/rtb.js";
import AdmollyUK from "../../../assets/js/Api/admollyUK.js";
import AdmollyDE from "../../../assets/js/Api/admollyDE.js";
import ReutovaUS from "../../../assets/js/Api/reutovaUS.js";
import ReutovaCA from "../../../assets/js/Api/reutovaCA.js";
import ReutovaAU from "../../../assets/js/Api/reutovaAU.js";
import ReutovaIT from "../../../assets/js/Api/reutovaIT.js";
import ReutovaUK from "../../../assets/js/Api/reutovaUK.js";
import ReutovaDE from "../../../assets/js/Api/reutovaDE.js";
import ImaxUS from "../../../assets/js/Api/imaxUS.js";
import ImaxCZ from "../../../assets/js/Api/imaxCZ.js";
import ImaxSK from "../../../assets/js/Api/imaxSK.js";
import ImaxUK from "../../../assets/js/Api/imaxUK.js";
import DatingPartnersCL from "../../../assets/js/Api/datingpartnersCL.js";
import DatingPartnersAR from "../../../assets/js/Api/datingpartnersAR.js";
import DatingPartnersCO from "../../../assets/js/Api/datingpartnersCO.js";
import DatingPartnersMX from "../../../assets/js/Api/datingpartnersMX.js";
import DirectOffer from "../../../assets/js/Api/directOffer.js";
import AdveryIT from "../../../assets/js/Api/adveryIT.js";

import config from "../../js/config/config.json";
import services from "../../js/config/services.json";
import Params from "../../../assets/js/helper/urlParameters.js";

import { ref, computed } from "vue";

const apiRequest = async (data, reference, errorMessage) => {
  const Rules = ref((await import(`../../../assets/js/config/antifraud/rules/${data.device.geo.country_code}.json`)).default);

  data.antifraud = {
    FraudLogix: {},
    Reoon: {},
    Outlook: {},
  };

  errorMessage.value = false;
  reference.value = true;
  let loopCounter = 0;
  let tmpError = "";

  let date = new Date();
  let timestamp = date.getTime();
  data.key = timestamp;
  // Inject rules into data
  data.rules = Rules.value;
  data.isSafeIP = false;
  data.isSafeEmail = false;

  var selected = Params().select;
  selected.push("RTB");
  selected.push("Smartlink");

  try {
    const page_path = window.location.pathname;
    
    if (data.email.toLowerCase().includes("@outlook.com")) {
      selected = ["Smartlink"];
      data.antifraud.Outlook = "TRUE";
    } else {
      const allowedTrafIds = ["109", "108", "128", "121"];
      const trafId = String(Params()["traf_id"]);

      if (allowedTrafIds.includes(trafId)) {
        Rules.value.validate = false;
      }

      if (Rules.value.validate && page_path != "/splash") {
        if (Params()["ipCheck"] != "false") {
          await axios.get("https://sexydates.live/api/fraudlogix.php", { params: { ip: data.device.ip } }).then((resp) => {
            data.antifraud.FraudLogix = resp.data;
            data.isSafeIP = resp.data.status == 1 ? true : false;
          });
        }

        if ((data.isSafeIP == true || Params()["ipCheck"] == "false") && Params()["eCheck"] != "false") {
          const reoonEndpoint = "https://emailverifier.reoon.com/api/v1/verify?email=" + data.email + "&key=KtXXvWJgP3zghCo6hjwg5UlYiyuOvzOl&mode=power";
          try {
            const reresp = await axios.get(reoonEndpoint);
            data.antifraud.Reoon = reresp.data;
            data.isSafeEmail = reresp.data.overall_score >= 75 ? true : false;

            if (data.isSafeEmail == false) {
              selected = ["Smartlink"];
            }
          } catch (error) {
            console.error("Error fetching Reoon API:", error);
          }
        } else {
          selected = ["RTB", "Smartlink"];
        }
      } else {
        selected.push("RTB");
        selected.push("Smartlink");
      }
    }
  } catch (error) {
    console.error("Error in validation checks:", error);
  }

  // Strip out duplicate entry in Selected array
  selected = [...new Set(selected)];

  const requests = {
    Admolly: Admolly,
    Adsempire: Adsempire,
    Huge: Huge,
    Ovation: Ovation,
    OvationFrOne: OvationFrOne,
    OvationFrTwo: OvationFrTwo,
    OvationIT: OvationIT,
    OvationCA: OvationCA,
    OvationFR: OvationFR,
    Rebll: Rebll,
    RebllFr: RebllFr,
    TogetherFR: TogetherFR,
    Together: Together,
    Hoinew: Hoinew,
    Wiimax: Wiimax,
    Ais: Ais,
    Advidi: Advidi,
    AdvidiFr: AdvidiFr,
    Cummission: Cummission,
    Smartlink: Smartlink,
    AdmollyAU: AdmollyAU,
    AdmollyCA: AdmollyCA,
    AdsempireCA: AdsempireCA,
    AdsempireAU: AdsempireAU,
    AdsempireFR: AdsempireFR,
    Trafficpartner: Trafficpartner,
    Mic: Mic,
    MicDE: MicDE,
    MicNL: MicNL,
    MicIT: MicIT,
    MicAU: MicAU,
    MicFR: MicFR,
    AdsempireAT: AdsempireAT,
    AdsempireDE: AdsempireDE,
    AdsempireNL: AdsempireNL,
    AdsempireCH: AdsempireCH,
    TrafficpartnerAU: TrafficpartnerAU,
    TrafficpartnerCA: TrafficpartnerCA,
    TrafficpartnerFR: TrafficpartnerFR,
    AisAT: AisAT,
    AisCH: AisCH,
    AisDE: AisDE,
    Lovado: Lovado,
    HoiUS: HoiUS,
    MicAT: MicAT,
    MicCH: MicCH,
    LovadoAT: LovadoAT,
    LovadoCH: LovadoCH,
    AdvidiIT: AdvidiIT,
    AdvidiFrTwo: AdvidiFrTwo,
    AdvidiCA: AdvidiCA,
    TrafficpartnerCH: TrafficpartnerCH,
    TrafficpartnerDE: TrafficpartnerDE,
    TogetherDE: TogetherDE,
    TrafficpartnerNL: TrafficpartnerNL,
    LeadpartnerAT: LeadpartnerAT,
    LeadpartnerCH: LeadpartnerCH,
    LeadpartnerDE: LeadpartnerDE,
    Whitelabels: Whitelabels,
    WiimaxFR: WiimaxFR,
    WiimaxNL: WiimaxNL,
    MicCH2: MicCH2,
    MicBE: MicBE,
    AdsempireSE: AdsempireSE,
    AdsempireJP: AdsempireJP,
    AdsempireNZ: AdsempireNZ,
    AdsempireCZ: AdsempireCZ,
    AdsempireMX: AdsempireMX,
    WiimaxBE: WiimaxBE,
    WiimaxCZ: WiimaxCZ,
    WiimaxGR: WiimaxGR,
    MicFR2: MicFR2,
    MicPT: MicPT,
    MicPL: MicPL,
    MicSE: MicSE,
    MicNO: MicNO,
    MicDK: MicDK,
    MicSI: MicSI,
    MicHU: MicHU,
    MicGR: MicGR,
    MicCZ: MicCZ,
    FlirtyadsAU: FlirtyadsAU,
    FlirtyadsNZ: FlirtyadsNZ,
    FlirtyadsIE: FlirtyadsIE,
    FlirtyadsCA: FlirtyadsCA,
    FlirtyadsUS: FlirtyadsUS,
    FlirtyadsIT: FlirtyadsIT,
    FlirtyadsZA: FlirtyadsZA,
    FlirtyadsSE: FlirtyadsSE,
    FlirtyadsDE: FlirtyadsDE,
    FlirtyadsUK: FlirtyadsUK,
    FlirtyadsNL: FlirtyadsNL,
    FlirtyadsFR: FlirtyadsFR,
    TogetherNZ: TogetherNZ,
    TogetherMX: TogetherMX,
    TogetherBR: TogetherBR,
    TogetherCL: TogetherCL,
    TogetherAU: TogetherAU,
    FlirtyadsAUmob: FlirtyadsAUmob,
    FlirtyadsNZmob: FlirtyadsNZmob,
    FlirtyadsIEmob: FlirtyadsIEmob,
    FlirtyadsCAmob: FlirtyadsCAmob,
    FlirtyadsUSmob: FlirtyadsUSmob,
    FlirtyadsITmob: FlirtyadsITmob,
    FlirtyadsZAmob: FlirtyadsZAmob,
    FlirtyadsSEmob: FlirtyadsSEmob,
    FlirtyadsDEmob: FlirtyadsDEmob,
    FlirtyadsUKmob: FlirtyadsUKmob,
    FlirtyadsNLmob: FlirtyadsNLmob,
    FlirtyadsFRmob: FlirtyadsFRmob,
    FlirtyadsUS2: FlirtyadsUS2,
    FlirtyadsUSmob2: FlirtyadsUSmob2,
    FlirtyadsUS3: FlirtyadsUS3,
    FlirtyadsUSmob3: FlirtyadsUSmob3,
    FlirtyadsUS4: FlirtyadsUS4,
    FlirtyadsUSmob4: FlirtyadsUSmob4,
    FlirtyadsUS5: FlirtyadsUS5,
    FlirtyadsUSmob5: FlirtyadsUSmob5,
    FlirtyadsUS6: FlirtyadsUS6,
    FlirtyadsUSmob6: FlirtyadsUSmob6,
    FlirtyadsFR2: FlirtyadsFR2,
    FlirtyadsFRmob2: FlirtyadsFRmob2,
    FlirtyadsFR3: FlirtyadsFR3,
    FlirtyadsFRmob3: FlirtyadsFRmob3,
    FlirtyadsIT2: FlirtyadsIT2,
    FlirtyadsITmob2: FlirtyadsITmob2,
    FlirtyadsIT3: FlirtyadsIT3,
    FlirtyadsITmob3: FlirtyadsITmob3,
    FlirtyadsAU2: FlirtyadsAU2,
    FlirtyadsAUmob2: FlirtyadsAUmob2,
    FlirtyadsES: FlirtyadsES,
    FlirtyadsESmob: FlirtyadsESmob,
    FlirtyadsES2: FlirtyadsES2,
    FlirtyadsESmob2: FlirtyadsESmob2,
    SevenClicksAU: SevenClicksAU,
    SevenClicksCA: SevenClicksCA,
    SevenClicksGB: SevenClicksGB,
    SevenClicksIE: SevenClicksIE,
    SevenClicksNZ: SevenClicksNZ,
    SevenClicksUS: SevenClicksUS,
    SevenClicksAT: SevenClicksAT,
    SevenClicksCH: SevenClicksCH,
    SevenClicksDE: SevenClicksDE,
    FlirtyadsPT: FlirtyadsPT,
    FlirtyadsPTmob: FlirtyadsPTmob,
    DatingstarsAU: DatingstarsAU,
    DatingstarsBE: DatingstarsBE,
    DatingstarsNL: DatingstarsNL,
    DatingleadsAU: DatingleadsAU,
    DatingleadsBE: DatingleadsBE,
    DatingleadsNL: DatingleadsNL,
    DatingleadsUS: DatingleadsUS,
    DatingleadsDE: DatingleadsDE,
    MireliaUS: MireliaUS,
    MireliaUK: MireliaUK,
    MireliaCA: MireliaCA,
    MireliaAU: MireliaAU,
    MireliaNZ: MireliaNZ,
    MireliaAT: MireliaAT,
    MireliaFR: MireliaFR,
    MireliaDE: MireliaDE,
    MireliaCH: MireliaCH,
    AdsempireUK: AdsempireUK,
    SevenClicksNO: SevenClicksNO,
    SevenClicksSE: SevenClicksSE,
    SevenClicksDK: SevenClicksDK,
    MicUK: MicUK,
    DatingPartnersUS: DatingPartnersUS,
    DatingPartnersUK: DatingPartnersUK,
    DatingPartnersCA: DatingPartnersCA,
    ViceROIUS: ViceROIUS,
    ViceROICZ: ViceROICZ,
    ViceROICO: ViceROICO,
    ViceROIDE: ViceROIDE,
    MoarUS: MoarUS,
    Affmy: Affmy,
    MoarWellHelloUS: MoarWellHelloUS,
    MoarUK: MoarUK,
    MoarCA: MoarCA,
    DatingleadsCH: DatingleadsCH,
    DatingleadsAT: DatingleadsAT,
    DatingleadsUK: DatingleadsUK,
    DatingleadsCA: DatingleadsCA,
    DatingleadsNZ: DatingleadsNZ,
    DatingleadsFR: DatingleadsFR,
    DatingleadsES: DatingleadsES,
    DatingleadsIT: DatingleadsIT,
    DatingleadsIE: DatingleadsIE,
    DatingleadsPL: DatingleadsPL,
    DatingleadsPT: DatingleadsPT,
    DatingleadsGR: DatingleadsGR,
    DatingleadsTR: DatingleadsTR,
    DatingleadsCZ: DatingleadsCZ,
    DatingleadsFI: DatingleadsFI,
    DatingleadsSE: DatingleadsSE,
    DatingleadsDK: DatingleadsDK,
    DatingleadsNO: DatingleadsNO,
    DatingleadsIL: DatingleadsIL,
    DatingleadsRO: DatingleadsRO,
    DatingleadsRS: DatingleadsRS,
    RebllIT: RebllIT,
    RebllUK: RebllUK,
    RebllUK2: RebllUK2,
    RebllUS2: RebllUS2,
    RebllUS3: RebllUS3,
    FlirtKingsUK: FlirtKingsUK,
    FlirtKingsCA: FlirtKingsCA,
    FlirtKingsAU: FlirtKingsAU,
    HoiDK: HoiDK,
    WiimaxUK: WiimaxUK,
    WiimaxAU: WiimaxAU,
    AdveryUK: AdveryUK,
    AdveryUS: AdveryUS,
    AdveryCA: AdveryCA,
    Loudbids: Loudbids,
    WiimaxDE: WiimaxDE,
    WiimaxAT: WiimaxAT,
    WiimaxCH: WiimaxCH,
    AdveryAU: AdveryAU,
    AdveryFR: AdveryFR,
    ExoClickFR: ExoClickFR,
    ExoClickDACH: ExoClickDACH,
    ExoClickUS: ExoClickUS,
    ExoClickWW: ExoClickWW,
    OvationUK: OvationUK,
    ImaxDE: ImaxDE,
    ImaxIT: ImaxIT,
    ImaxNO: ImaxNO,
    ImaxPL: ImaxPL,
    ImaxPT: ImaxPT,
    DatingleadsFbIT: DatingleadsFbIT,
    DatingleadsFbNL: DatingleadsFbNL,
    DatingleadsFbBE: DatingleadsFbBE,
    TrafficcompannyNL: TrafficcompannyNL,
    TrafficcompannyFR: TrafficcompannyFR,
    TrafficcompannyDE: TrafficcompannyDE,
    TrafficcompannyIT: TrafficcompannyIT,
    SalamandraFR: SalamandraFR,
    HoiUsFB: HoiUsFB,
    EmediabuildersUS: EmediabuildersUS,
    EmediabuildersCA: EmediabuildersCA,
    EmediabuildersAU: EmediabuildersAU,
    EmediabuildersNZ: EmediabuildersNZ,
    EmediabuildersNL: EmediabuildersNL,
    EmediabuildersDK: EmediabuildersDK,
    EmediabuildersDE: EmediabuildersDE,
    EmediabuildersGB: EmediabuildersGB,
    RTB: RTB,
    TrafficMansionUS: TrafficMansionUS,
    TrafficMansionCA: TrafficMansionCA,
    TrafficMansionAU: TrafficMansionAU,
    TrafficMansionGB: TrafficMansionGB,
    TrafficMansionIE: TrafficMansionIE,
    TrafficMansionNZ: TrafficMansionNZ,
    AdmollyUK: AdmollyUK,
    AdmollyDE: AdmollyDE,
    ReutovaUS: ReutovaUS,
    ReutovaCA: ReutovaCA,
    ReutovaAU: ReutovaAU,
    ReutovaIT: ReutovaIT,
    ReutovaUK: ReutovaUK,
    ReutovaDE: ReutovaDE,
    ImaxUS: ImaxUS,
    ImaxCZ: ImaxCZ,
    ImaxSK: ImaxSK,
    ImaxUK: ImaxUK,
    DatingPartnersCL: DatingPartnersCL,
    DatingPartnersAR: DatingPartnersAR,
    DatingPartnersCO: DatingPartnersCO,
    DatingPartnersMX: DatingPartnersMX,
    DirectOffer: DirectOffer,
    AdveryIT: AdveryIT,
  };
  //Change the service name to key
  function getServiceKeyByValue(value) {
    if (!value) return null;

    const cleanedValue = value.trim().toLowerCase();

    return (
      Object.keys(services).find((key) => {
        const serviceName = services[key];
        return serviceName && serviceName.toLowerCase() === cleanedValue;
      }) || null
    );
  }
  //Change the key to service name
  function getServiceValueByKey(key) {
    if (!key) return null;

    const normalizedKey = String(key).trim();
    return services.hasOwnProperty(normalizedKey) ? services[normalizedKey] : null;
  }

  // Redirect to tracker if already registered (In progress)

  if (selected) {
    try {
      var selectedServices = [];
      for (let x in selected) {
        var val = getServiceKeyByValue(selected[x]);
        if (val !== null) {
          selectedServices.push(val);
        }
      }
      var url = `https://trakle02.online/api/check_service_rejected?services=${selectedServices.join(",")}&email=${data.email}`;

      var rej_resp = await fetch(url);
      if (rej_resp.ok) {
        var reject_resp = await rej_resp.json();
        var newSelected = [];

        for (let x in reject_resp) {
          var newval = getServiceValueByKey(reject_resp[x]);
          if (newval !== null) {
            newSelected.push(newval);
          }
        }
        newSelected.push("RTB");
        newSelected.push("Smartlink");
        selected = newSelected;
      }
    } catch (err) {
      console.log("Something went wrong:", err);
    }

    try {
      if (Params()["alreadyRegistered"] == "true") {
        selected = ["Smartlink"];
      }
    } catch (err) {
      console.error("Something is wrong with IP checking:", err);
    }

    // Loop through selected services
    for (let x in selected) {
      if (selected[x] in requests) {
        const apiRequest = await requests[selected[x]](data);
        if (apiRequest.data.code == 200) {
          tmpError = false;
          let redirect = apiRequest.data.url;
          if (apiRequest.data.source == "Smartlink") {
            var paramsUrl = Params().url;
            if (redirect.includes("?")) {
              // Merge both, ensuring only one '?'
              redirect += "&" + paramsUrl.substring(1);
            } else {
              redirect += paramsUrl;
            }
          }

          // Add email in URL Params forcefully
          redirect = redirect + "&email=" + data.email;

          if (selected[0] == "Smartlink") {
            redirect = redirect;
          }

          window.location = redirect;

          window.open(redirect, "_blank").focus();
          window.location = "https://trakle01.online/tracker/113" + Params().url;
          // window.location(redirect);
          return false;
        } else {
          tmpError = apiRequest.data.message;
        }
      }

      if (loopCounter == selected.length - 1) {
        errorMessage.value = tmpError;
      }
      loopCounter++;
    }
  } else {
    /**
     * Initiate Waterfall call approach
     */
    for (let seq in requests) {
      const apiRequest = await requests[seq](data);
      if (apiRequest.data.code == 200) {
        tmpError = false;
        window.location = PrepUrl(apiRequest.data.url, Params().url);
        break;
      } else {
        tmpError = apiRequest.data.message;
      }

      if (loopCounter == requests.length - 1) {
        errorMessage.value = tmpError;
      }
      loopCounter++;
    }
  }

  reference.value = false;
};

export default apiRequest;
