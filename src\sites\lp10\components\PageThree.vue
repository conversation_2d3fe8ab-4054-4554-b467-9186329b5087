<script setup>
import { ref, defineProps, inject } from "vue";
import Steps from "../../lp9/components/views/StepsMarker.vue";
import Language from "../../../assets/js/helper/Language.js";
import Searching from "../../../sites/lp12/components//views/Searching.vue";

const props = defineProps(["steps", "moveNextSlide", "moveBackSlide", "language", "location", "inputs"]);
const steps = ref(props.steps);
const assets = inject("assets");

const selectPreference = (preference) => {
  props.moveNextSlide(preference);
};
</script>

<template>
  <div class="container my-5">
    <div class="card p-4">
      <div class="header-logo">
        <img src="../../../assets/flirty.png" class="logofli" alt="" />
      </div>
      <form>
        <div class="form-group">
          <label for="gender" class="w-100 text-center fs-5">{{ Language.question_looking }}</label>
          <hr />
          <div class="d-flex justify-content-around my-3 mt-5">
            <div>
              <button type="button" class="btn btn-outline" for="option1" @click="selectPreference('men')"><img class="me-2" src="../../../assets/icons8-male.png" style="height: 24px" alt="" />{{ Language.guy }}</button>
            </div>
            <div>
              <button type="button" class="btn btn-outline" for="option2" @click="selectPreference('women')"><img class="me-2" src="../../../assets/icons8-female.png" style="height: 24px" alt="" />{{ Language.girl }}</button>
            </div>
          </div>
        </div>
      </form>
      <Steps :step="steps" />
      <div class="disclaimer mt-2">
        <div class="progress mb-3" role="progressbar" aria-label="Info example" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100">
          <div class="progress-bar bg-info" style="width: 42%"></div>
        </div>
        <div class="d-flex justify-content-center">
          <Searching :location="location" :language="Language" />
        </div>
      </div>
    </div>
  </div>
</template>

<style>
body {
  background-color: #f0f8ff;
}
.card {
  max-width: 400px;
  margin: auto;
  padding: 20px;
  border-radius: 15px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}
.header-logo {
  text-align: center;
  margin-bottom: 20px;
}
.header-logo img {
  width: 150px;
}
.header-text {
  text-align: center;
  font-size: 1.5rem;
  margin-bottom: 30px;
}
.form-group {
  margin-bottom: 15px;
}
.btn-primary {
  background-color: #6a1b9a;
  border-color: #6a1b9a;
  width: 100%;
  font-size: 1.2rem;
}
.disclaimer {
  text-align: center;
  margin-top: 20px;
}
.disclaimer small {
  color: #6c757d;
}

.btn-outline {
  width: 10em !important;
  height: 4em !important;
  border-color: #6a1b9a;
  color: black;
}

.btn-outline:hover {
  background-color: #6a1b9a;
  color: white;
}

.styled-paragraph {
  font-style: italic;
  font-size: 1rem;
  color: palevioletred !important;
}

.card {
  height: 30em;
  width: 25em;
}
</style>
