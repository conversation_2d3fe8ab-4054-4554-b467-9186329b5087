<?php
include_once('./log.php');

$config = file_exists('./config/api.json') ? json_decode(file_get_contents('./config/api.json')) : null;

/**
 * Make sure the configuration is not missing or empty
 */
if($config == null){
	$fileMissing = json_encode([
		'code' => 401,
		'message' => 'Api configuration file missing',
	]);
	die($fileMissing);
}

$startTime	= microtime(true);
$offer		= isset($_GET['offer']) ? $_GET['offer'] : 'us';
$endpoint	= $config->mic->endpoint->$offer;

$hash		= isset($_GET['traf_id']) ? substr(md5($_GET['traf_id']), 0, 16) : "";
$api_geo	= ['nl', 'be', 'at', 'ch', 'se', 'dk', 'no'];


if(in_array($offer, $api_geo)){
	$data = [
		'apiKey'		=> $config->mic->apiKey->$offer,
		'leadFields'	=> [
			'nickname'		=> $_GET['nickname'],
			'email'			=> $_GET['email'],
			'EHAWK_TALON'	=> $_GET['talon'],
			'CONFIRMED_TOC'	=> 'yes'
		],
		'QueryValues'	=> [
			'sub1'	=> $_GET['click_id'],
			'sub2'	=> $hash,
			'sub3'	=> $_GET['cmp']
		],
		'clientValues'	=> [
			'USERAGENT'			=> $_GET['device']['raw'],
			'CLIENTIPADDRESS'	=> $_GET['device']['ip']
		]
	];
}else{
	$data = [
		'nas'			=> $_GET['click_id'],
		'pi'			=> $_GET['cmp'],
		'nickname'		=> $_GET['nickname'],
		'email'			=> $_GET['email'],
		'password'		=> $_GET['password'],
		'dob'			=> $_GET['dob']
	];
}

$ch = curl_init($endpoint);
curl_setopt_array($ch, array(
	CURLOPT_POST	=> true,
	CURLOPT_SSL_VERIFYHOST => false,
	CURLOPT_SSL_VERIFYPEER => false,
	CURLOPT_RETURNTRANSFER => true,
	CURLOPT_HTTPHEADER => array(
		'Content-Type: application/json'
	),
	CURLOPT_POSTFIELDS => json_encode($data)
));

$response = curl_exec($ch);
$responseData = json_decode($response, true);
$httpcode = curl_getinfo($ch, CURLINFO_RESPONSE_CODE);
curl_close($ch);

// Do not include Email in data storage
$data['email'] = '************';

$return = [
	'code' => 200,
	'message' => '',
	'url' => '',
	'time' => '0',
	'request_data' => $data,
	'response_data' => '',
	'source' => 'Mic '.$offer,
	'offer' => $offer
];

/**
 * 1. Check if user created successfully - build success response
 * 2. If user creation failed - build error response
 */
if($response != false){
	if(in_array($offer, $api_geo)){
		if(is_array($responseData) && $responseData['success'] == false){
			$return['message'] = json_encode($responseData['errors']);
			$return['code'] = $httpcode;
		}else if($responseData['success'] == true){
			$jsonResponse = json_decode($response);
			$return['message'] = 'Account successfully created';
			$return['url'] = $responseData['forwardUrl'];
		}
	}else{
		if(is_array($responseData) && $responseData['SoiCreated'] == false){
			$return['message'] = json_encode($responseData['Errors']);
			$return['code'] = $responseData['HttpStatusCode'];
		}else if($responseData['SoiCreated'] == true){
			$jsonResponse = json_decode($response);
			$return['message'] = 'Account successfully created';
			$return['url'] = $responseData['ForwardUrl'];
		}
	}
}else{
	$responseData = 'API Response is ' . $response;
}

$endTime = microtime(true);
$return['key']				= $_GET['key'];
$return['response_data']	= $responseData ? $responseData : $response;
$return['start_time']		= $startTime;
$return['end_time']			= $endTime;
$return['endpoint']			= $endpoint;
$return['time'] 			= number_format( ( $endTime - $startTime ), 2);
$return['click_id']			= $_GET['click_id'];
$return['source_id']		= $_GET['source_id'];
$return['is_sellable']		= $_GET['is_sellable'];

$return['email']			= $_GET['email'];
$return['username']			= $_GET['nickname'];
$return['password']			= $_GET['password'];
$return['gender']			= $_GET['gender'];
$return['looking']			= $_GET['looking'];
$return['dob']				= $_GET['dob'];

$return['lander']			= $_GET['lander'];
$return['city']				= $_GET['city'];
$return['device']			= $_GET['device'];
$return['http']				= $_GET['http'];
$return['index_id']			= $_GET['index_id'];
$return['antifraud']		= isset($_GET['antifraud']) ? json_encode($_GET['antifraud']) : '';

// Send Data to Log API
$log	= new Log($return);
$send	= $log->send($config->log->endpoint);

echo json_encode($return);