import axios from 'axios'
import endpoints from '../../../assets/js/config/endpoints.json'
import params from '../../../assets/js/helper/urlParameters.js'

const hoius = async (formData) => {

    const httpData = {
        username    : formData.username,
        email       : formData.email,
        password    : formData.password,
        birthdate   : formData.birth_year+'-'+formData.birth_month+'-'+formData.birth_day,
        gender      : formData.gender,
        city        : formData.location,
        zip         : formData.postal_code,
        country     : formData.country_code,
        click_id    : formData.click_id,
        key         : formData.key,
        source_id   : formData.source_id,
        is_sellable : !formData.is_sellable,
        device      : formData.device,
        lander      : formData.lander,
        http        : formData.http,
        cmp         : formData.cmp,
        looking     : formData.seek,
        valid_geo   : "US",
        index_id    : "39",
        traf_id     : formData.traf_id,
        antifraud   : formData.antifraud,
    }

    return await axios.get(endpoints.hoius, { params: httpData })
}

export default hoius