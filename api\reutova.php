<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);
include_once('./log.php');

$config = file_exists('./config/api.json') ? json_decode(file_get_contents('./config/api.json')) : null;
/**
 * Make sure the configuration is not missing or empty
 */
if($config == null){
	$fileMissing = json_encode([
		'code' => 401,
		'message' => 'Api configuration file missing',
	]);
	die($fileMissing);
}

$startTime = microtime(true);

$md5Hash = $_GET['traf_id'] ? md5($_GET['traf_id']) : "";
$traf_id = $md5Hash ? substr($md5Hash, 0, 16) : ""; 

$data = [
	'CampaignId' 	=> $config->reutova->campaign_id,
	'BirthDate' 	=> $_GET['birth_year'] . '-' . $_GET['birth_month'] . '-' . $_GET['birth_day'] ,
	'Email' 		=> $_GET['email'],
	'Gender' 		=> $_GET['gender'],
	'Password' 		=> $_GET['password'],
	'Username' 		=> $_GET['username'],
	'Ip' 			=> $_GET['device']['ip'],
	'UserAgent' 	=> $_GET['device']['raw'],
	'AcceptLanguage' => $_GET['locale'],
	'RegistrationId' => $_GET['click_id'],
	'Vars' => [
		'source' => $_GET['t1'],
		'subaff' => $_GET['t2'],
		'traf_id' => $traf_id
	]
];

$endpoint = $config->reutova->endpoint;


$ch = curl_init($endpoint);
curl_setopt_array($ch, array(
    CURLOPT_POST => true,
    CURLOPT_POSTFIELDS => json_encode($data),
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_HTTPHEADER => array(
        'Content-Type: application/json'
    )
));

$response = curl_exec($ch);
$responseData = json_decode(trim($response), true);
$httpcode = curl_getinfo($ch, CURLINFO_RESPONSE_CODE);
curl_close($ch);

// Do not include Email in data storage
$data['email'] = '************';

$return = [
	'code' => 200,
	'message' => '',
	'url' => '',
	'time' => '0',
	'request_data' => $data,
	'response_data' => '',
	'source' => 'reutova - ' . $_GET['offer']
];


/**
 * 1. Check if user created successfully - build success response
 * 2. If user creation failed - build error response
 */
if($response != false){
	if(in_array($responseData['Code'], [0, 200]) && !empty($responseData['LoginUrl'])){
		$return['message'] = "Account created successfully";
		$return['code'] = 200;
		$return['url'] = $responseData['LoginUrl'];
	}
	else{
		$return['message'] = $responseData['Error'];
		$return['code'] = $responseData['Code'];
	}
}else{
	$responseData = 'Unable to connect API';
}

$endTime = microtime(true);
$return['key']				= $_GET['key'];
$return['response_data']	= $responseData;
$return['start_time']		= $startTime;
$return['end_time']			= $endTime;
$return['endpoint']			= $config->reutova->endpoint;
$return['time'] 			= number_format( ( $endTime - $startTime ), 2);
$return['click_id']			= $_GET['click_id'];
$return['source_id']		= $_GET['source_id'];
$return['is_sellable']		= $_GET['is_sellable'];

$return['email']			= $_GET['email'];
$return['username']			= $_GET['username'];
$return['password']			= $_GET['password'];
$return['gender']			= $_GET['gender'];
$return['looking']			= $_GET['looking'];
$return['dob']				= $_GET['birth_year'].'-'.$_GET['birth_month'].'-'.$_GET['birth_day'];

$return['device']			= $_GET['device'];
$return['city']				= isset($_GET['device']['geo']['city']) ? $_GET['device']['geo']['city'] : (isset($_GET['city']) ? $_GET['city'] : '');
$return['lander']			= $_GET['lander'];
$return['http']				= $_GET['http'];
$return['index_id']			= $_GET['index_id'];
$return['antifraud']		= isset($_GET['antifraud']) ? json_encode($_GET['antifraud']) : '';

// Send Data to Log API
$log	= new Log($return);
$send	= $log->send($config->log->endpoint);

echo json_encode($return);