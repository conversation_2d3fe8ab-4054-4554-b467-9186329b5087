<script setup>
import { ref, defineProps, onMounted } from "vue";
import Steps from "../../lp9/components/views/StepsMarker.vue";
import Language from "../../../assets/js/helper/Language.js";
import Api from "../../../assets/js/helper/api.js";
import Swal from "sweetalert2";
import Searching from "../../lp14/components/views/Searching.vue";

const props = defineProps(["inputs", "steps", "moveNextSlide"]);
const steps = ref(props.steps);
const apiErrorMessage = ref(false);
const disableSubmitBtn = ref(false);
const showSearching = ref(false);

const validationMessages = ref({
  username: false,
  dob: false,
  email: false,
  password: false,
  privacy: false,
  privacy_service: false,
});

// onMounted(() => {
//   const script = document.createElement("script");
//   script.src = "https://www.google.com/recaptcha/api.js";
//   document.head.appendChild(script);
// });

// const onSubmit = async (token) => {
//   if (await validateForm()) {
//     showSearching.value = true;

//     let formData = {};
//     for (let x in props.inputs) {
//       formData[x] = props.inputs[x]["value"];
//     }
//     formData.recaptchaToken = token;

//     await new Promise((resolve) => setTimeout(resolve, 1000));
//     Api(formData, disableSubmitBtn, apiErrorMessage);
//   }
// };

// window.onSubmit = onSubmit;

onMounted(() => {
  window.dataLayer?.push({
    event: "page_view",
  });

  const uncheckedCountries = ["AT", "BE", "BG", "HR", "CY", "CZ", "DK", "EE", "FI", "FR", "DE", "GR", "HU", "IE", "IT", "LV", "LT", "LU", "MT", "NL", "PL", "PT", "RO", "SK", "SI", "ES", "SE", "AD", "BA", "IS", "LI", "MC", "MD", "ME", "MK", "NO", "RS", "CH", "UA", "GB", "VA", "SM"];

  if (props.inputs.device.value?.geo?.country_code && !uncheckedCountries.includes(props.inputs.device.value.geo.country_code)) {
    props.inputs.privacy.value = true;

    const privacyCheckbox = document.getElementById("privacyCheck");
    if (privacyCheckbox) {
      privacyCheckbox.checked = true;
    }
  }
});

const validateForm = async (event) => {
  // Push event to trigger GTM tag
  window.dataLayer?.push({
    event: "button_click",
    Click_Classes: "button_click", // Matches trigger condition "Click Classes contains button_click"
    _event: "gtm.linkClick", // Matches trigger condition "_event equals gtm.linkClick"
    _triggers: "203887914_4", // Matches trigger condition "_triggers matches RegEx"
    "gtm.element": event.target,
    "gtm.elementClasses": "button_click g-recaptcha btn border btn-outline-12 rounded-5 px-5",
    "gtm.elementId": "submit-button",
  });

  // Log for debugging
  console.log("GTM Event Pushed:", {
    event: "button_click",
    Click_Classes: "button_click",
    _event: "gtm.linkClick",
    _triggers: "203887914_4",
  });

  if (!validateUsername()) {
    return false;
  }
  if (!validateDOB()) {
    return false;
  }
  if (!validateEmail()) {
    return false;
  }
  if (!validatePassword()) {
    const alertMessage = validationMessages.value.password;
    Swal.fire({
      text: alertMessage,
      customClass: {
        popup: "custom-swal-popup",
      },
    });
    return false;
  }
  if (!validatePrivacy()) {
    const alertMessage = validationMessages.value.privacy;
    Swal.fire({
      text: alertMessage,
      customClass: {
        popup: "custom-swal-popup",
      },
    });
    return false;
  }

  // Show searching screen
  showSearching.value = true;

  // Prepare form data
  let formData = {};
  for (let x in props.inputs) {
    formData[x] = props.inputs[x]["value"];
  }

  // Wait for 3 seconds before making API call
  await new Promise((resolve) => setTimeout(resolve, 1000));

  // Make API call
  Api(formData, disableSubmitBtn, apiErrorMessage);

  return true;
  e;
};

const validatePrivacy = () => {
  if (props.inputs.privacy.value == false) {
    validationMessages.value.privacy = Language.alert_update;
    return false;
  } else {
    validationMessages.value.privacy = false;
    return true;
  }
};

// const validatePrivacyService = () => {
//   if (props.inputs.privacy_service.value == false) {
//     validationMessages.value.privacy_service = Language.alert_update;
//     return false;
//   } else {
//     validationMessages.value.privacy_service = false;
//     return true;
//   }
// };

const validatePassword = () => {
  let x = props.inputs.password.value;
  if (x.length > 14 || x.length < 6 || validator(x) == false || explicitValidator(x)) {
    validationMessages.value.password = Language.password_error_2;
    return false;
  } else {
    validationMessages.value.password = false;
    return true;
  }
};

const urlParams = new URLSearchParams(window.location.search);
const isUsernameZero = urlParams.get("username") === "all";

const validateUsername = () => {
  let x = props.inputs.username.value;
  const usernameRegex = /^(?=.*[0-9])(?=.*[a-zA-Z]).{6,14}$/;
  const usernameSimpleRegex = /^.{6,14}$/;

  if (isUsernameZero) {
    if (!usernameSimpleRegex.test(x)) {
      validationMessages.value.username = Language.username_error_3;
      return false;
    } else {
      validationMessages.value.username = false;
      return true;
    }
  } else {
    if (x.length > 14 || x.length < 6 || validator(x) == false || explicitValidator(x)) {
      validationMessages.value.username = Language.username_error_2;
      return false;
    } else {
      validationMessages.value.username = false;
      return true;
    }
  }
};

const validateEmail = () => {
  if (/^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,})+$/.test(props.inputs.email.value)) {
    validationMessages.value.email = false;
    return true;
  } else {
    validationMessages.value.email = Language.email_error;
    return false;
  }
};

const validator = (str) => {
  return /[0-9][a-zA-Z]|[a-zA-Z][0-9]/.test(str);
};

const explicitValidator = (str) => {
  const format = /[ `!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~]/;
  return format.test(str);
};

const validateDOB = () => {
  if (props.inputs.birth_day.value == "" || props.inputs.birth_year.value == "" || props.inputs.birth_month.value == "") {
    validationMessages.value.dob = Language.select_your_age;
    return false;
  } else {
    validationMessages.value.dob = false;
    return true;
  }
};
</script>

<template>
  <Searching v-if="showSearching" />

  <div class="container-fluid d-flex align-items-center justify-content-center min-vh-100">
    <div class="text-center">
      <p class="fs-1 mb-5 create">{{ Language.password_create }}</p>
      <div class="d-flex justify-content-center gap-5 mb-5 button-container">
        <input v-model="inputs.password.value" class="form-control rounded-5" style="width: 25em; height: 6em" type="password" id="password" placeholder="Enter Your Password" @keypress="handleKeyPress" />
        <button type="button" class="btn final-submit-btn g-recaptcha btn-danger rounded-5 fs-5" style="height: 5em; width: 20em" @click="(event) => validateForm(event)" :class="`${disableSubmitBtn ? 'disabled' : ''}`">
          {{ Language.btn_submit }}
          <div class="spinner-border spinner-grow-sm text-light" v-show="disableSubmitBtn" role="status">
            <span class="visually-hidden">{{ Language.loading }}</span>
          </div>
        </button>
      </div>

      <!-- <div class="d-flex justify-content-center gap-5 mb-5 button-container">
        <input v-model="inputs.password.value" class="form-control rounded-5" style="width: 25em; height: 6em" type="password" id="password" placeholder="Enter Your Password" @keypress="handleKeyPress" />
        <button type="button" class="btn g-recaptcha btn-danger rounded-5 fs-5" style="height: 5em; width: 20em" :class="`${disableSubmitBtn ? 'disabled' : ''}`" role="button" data-sitekey="6LcVHV4qAAAAAOz2INgLc2XPKtP5OdnJTktATdPS" data-callback="onSubmit" data-action="submit">
          {{ Language.btn_submit }}
          <div class="spinner-border spinner-grow-sm text-light" v-show="disableSubmitBtn" role="status">
            <span class="visually-hidden">{{ Language.loading }}</span>
          </div>
        </button>
      </div> -->
      <div class="others">
        <!-- <p>{{ Language.password_error_2 }}</p> -->
        <div class="d-flex agreement flex-column align-items-center justify-content-center">
          <div class="text-center" style="width: 35em !important">
            <div class="d-flex flex-column align-items-center justify-content-center">
              <div class="notice">
                <p class="agreement">
                  <strong> {{ Language.basic_info }}</strong> <br />
                  <strong>{{ Language.data_controller }}</strong> {{ Language.leads }} <a class="cursor-pointer text-danger" href="/privacy">{{ Language.info }}</a> <br />
                  <strong>{{ Language.purpose }}</strong> {{ Language.data_response }} <a class="cursor-pointer text-danger" href="/privacy">{{ Language.info }}</a> <br />
                  <strong>{{ Language.rights }}</strong
                  >{{ Language.data_access }} <a class="cursor-pointer text-danger" href="/privacy">{{ Language.info }}</a> <br />
                  <strong>{{ Language.additional_info }}</strong
                  >{{ Language.data_protect }} <a class="cursor-pointer text-danger" href="/privacy">{{ Language.privacy_policy }}</a>
                </p>
              </div>
              <div class="accept_agreed agreement">
                <div class="d-flex align-items-start mb-1">
                  <input type="checkbox" id="privacyCheck" v-model="inputs.privacy.value" class="custom-checkbox mt-1" />
                  <label for="privacyCheck" class="agree text-dark ms-2">{{ Language.privacy_agree }}</label>
                </div>
                <!-- <div class="d-flex align-items-start mb-1">
                  <input type="checkbox" id="serviceCheck" v-model="inputs.privacy_service.value" class="custom-checkbox mt-1" />
                  <label for="serviceCheck" class="agree text-dark ms-2">{{ Language.privacy_service_agree }}</label>
                </div> -->
                <!-- <div class="d-flex align-items-start mb-1">
                  <input type="checkbox" id="promotionCheck" v-model="inputs.promotion_agree.value" class="custom-checkbox mt-1" />
                  <label for="promotionCheck" class="agree text-dark ms-1">{{ Language.privacy_promotion_agree }}</label>
                </div> -->
              </div>
            </div>
          </div>
          <div
            v-if="
              ['Los Angeles', 'San Diego', 'Santa Monica', 'San Francisco', 'San Jose', 'Fresno', 'Sacramento', 'Long Beach', 'Oakland', 'Bakersfield', 'Anaheim', 'Santa Ana', 'Riverside', 'Stockton', 'Chula Vista', 'Irvine', 'Fremont', 'San Bernardino', 'Modesto', 'Oxnard', 'Fontana'].includes(
                inputs.location.value
              )
            "
            class="text-center mt-3"
          >
            <input type="checkbox" id="flexCheckDefault" v-model="inputs.is_sellable.value" />
            <label class="fs-6 ms-2" for="flexCheckDefault">{{ Language.data_permission }}</label>
          </div>
        </div>
      </div>
      <div class="text-center mt-2">
        <p class="text-dark" style="font-size: 10px">{{ Language.spam_alert }}</p>
      </div>
      <div class="steps d-flex gap-3 justify-content-center mt-3">
        <i class="fa-solid fa-star fs-3 text-white"></i>
        <i class="fa-solid fa-star fs-3 text-white"></i>
        <i class="fa-solid fa-star fs-3 text-white"></i>
        <i class="fa-solid fa-star fs-3 text-white"></i>
        <i class="fa-solid fa-star fs-3 text-white"></i>
        <i class="fa-solid fa-star fs-3 text-white"></i>
        <i class="fa-solid fa-star fs-3 text-white"></i>
      </div>
    </div>
    <div id="alert-message3" style="display: none">
      {{ Language.alert_update }}
    </div>
    <div id="alert-message" style="display: none">
      {{ Language.alert_update }}
    </div>
    <div id="alert-message2" style="display: none">
      {{ Language.password_error_2 }}
    </div>
    <Steps :step="steps" />
  </div>
</template>

<style scoped>
.notice {
  font-size: 8px !important;
}

.ms-1 {
  margin-right: 19px !important;
}

.accept_agreed .form-check-input {
  width: 20px; /* Set a fixed width */
  height: 20px; /* Set a fixed height */
}

.accept_agreed {
  font-size: 13px !important;
  text-align: start !important;
}
@media (max-width: 768px) {
  .fs-1 {
    font-size: 1.5rem;
  }
  .button-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-bottom: 4px !important;
    gap: 2; /* Adjust as needed */
  }
  .form-control {
    width: 20em !important;
    height: 4em !important;
  }
  .btn {
    width: 16em !important;
    height: 3em !important;
  }
  .steps {
    margin-top: 1em;
  }
  .others {
    width: 22em !important;
  }
  .agreement {
    width: 22em !important;
    margin-left: 1.5em !important;
    font-size: 13px !important;
    text-align: start !important;
  }
}
</style>
