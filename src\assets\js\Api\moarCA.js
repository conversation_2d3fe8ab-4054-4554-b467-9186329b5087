import axios from 'axios'
import endpoints from '../config/endpoints.json'
import params from '../helper/urlParameters.js'
import device from '../helper/deviceDetection.js'

const moar = async (formData) => {

   let httpData = {
        username            : formData.username,
        email               : formData.email,
        password            : formData.password,
        gender              : formData.gender,
        dob                 : formData.birth_year + "-" + formData.birth_month + "-" + formData.birth_day,
        sexual_orientation  : 'hetero',
        ua                  : device().ua_raw,
        click_id            : formData.click_id,
        subid               : formData.source_id,
        utm_medium          : device().device,
        key                 : formData.key,
        is_sellable         : !formData.is_sellable,
        device              : formData.device,
        lander              : formData.lander,
        http                : formData.http,
        city                : formData.location,
        offer               : 'ca',
        subid2              : formData.t2,
        looking             : formData.seek,
        index_id            : "178",
        traf_id             : formData.traf_id,
        t1                  : formData.t1,
        antifraud   : formData.antifraud,
    }
    
    return await axios.get(endpoints.moar, { params: httpData })
}

export default moar