import axios from 'axios'
import endpoints from '../../../assets/js/config/endpoints.json'
import device from '../../../assets/js/helper/deviceDetection.js'

const togetherFr = async (formData) => {

    let httpData = {
        email               : formData.email,
        gender              : formData.gender,
        dob                 : formData.birth_year + "-" + formData.birth_month + "-" + formData.birth_day,
        sexual_orientation  : 'hetero',
        ua                  : device().ua_raw,
        click_id            : formData.click_id,
        source_id           : formData.source_id,
        cmp                 : formData.cmp,
        utm_medium          : device().device,
        key                 : formData.key, // Unique Identifier for Logger
        is_sellable         : !formData.is_sellable,
        device              : formData.device,
        lander              : formData.lander,
        http                : formData.http,
        offer               : 'fr',
        username            : formData.username,
        password            : formData.password,
        looking             : formData.seek,
        city                : formData.location,
        t1                  : formData.t1,
        t2                  : formData.t2,
        index_id            : "20",
        traf_id             : formData.traf_id,
        antifraud   : formData.antifraud,
    }
    
    return await axios.get(endpoints.together, { params: httpData })
}

export default togetherFr