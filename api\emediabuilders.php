<?php
include_once('./log.php');

$config = file_exists('./config/api.json') ? json_decode(file_get_contents('./config/api.json')) : null;

/**
 * Make sure the configuration is not missing or empty
 */
if($config == null){
	$fileMissing = json_encode([
		'code' => 401,
		'message' => 'Api configuration file missing',
	]);
	die($fileMissing);
}

$startTime	= microtime(true);

$traf_id = $_GET['traf_id'] ? $_GET['traf_id'] : "";
$md5Hash = md5($traf_id);
$traf_id = substr($md5Hash, 0, 16);

$offer = $_GET['offer'];
$endpoint = $config->emediabuilders->endpoint;
$api_key = $config->emediabuilders->api_key;
$fields_string = ''; 

$curlauth = 'Authorization: '.$api_key;
                                    
$data = array(
    'email' 	=> $_GET['email'],
    'useragent' => $_GET['device']['raw'],
    'ip' 		=> $_GET['device']['ip'],
    'username' 	=> $_GET['username'],
    'password' 	=> $_GET['password'],
    'city' 		=> $_GET['device']['geo']['city'],
    'birthdate' => $_GET['dob'],
    'campaign' 	=> $traf_id,
    'clickid' 	=> $_GET['click_id']
);
                                    
foreach ($data as $key => $value) {
    $fields_string .= $key . '=' . $value . '&';
}

rtrim($fields_string, '&');
                                    
$ch = curl_init();
                                    
curl_setopt($ch, CURLOPT_URL, $endpoint);
curl_setopt($ch, CURLOPT_HTTPHEADER, [$curlauth]);
curl_setopt($ch, CURLOPT_POST, count($data));
curl_setopt($ch, CURLOPT_POSTFIELDS, $fields_string);
curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 1);
curl_setopt($ch, CURLOPT_TIMEOUT, 5);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
                                    
$response = curl_exec($ch);
$httpcode = curl_getinfo($ch, CURLINFO_RESPONSE_CODE);
curl_close($ch);


// Do not include Email in data storage
$data['email'] = '************';

$return = [
	'code' => 200,
	'message' => '',
	'url' => '',
	'time' => '0',
	'request_data' => $data,
	'response_data' => '',
	'source' => 'emediabuilders - ' . $offer,
	'offer' => $offer
];

/**
 * 1. Check if user created successfully - build success response
 * 2. If user creation failed - build error response
 */

// String is given as the response either a normal error string or url if successful
// 200 http code for both error and success so it needs to get checked if the response is a valid url or not

if (filter_var($response, FILTER_VALIDATE_URL)) {
    $return['message'] 	= 'Account successfully created';
	$return['code']		= 200;
	$return['url'] 		= $response;
} else {
    $return['message'] 	= $response;
	$return['code']		= 400;
}


$endTime = microtime(true);
$return['key']				= $_GET['key'];
$return['response_data']	= $response ? $response : '';
$return['start_time']		= $startTime;
$return['end_time']			= $endTime;
$return['endpoint']			= $endpoint;
$return['time'] 			= number_format( ( $endTime - $startTime ), 2);
$return['click_id']			= $_GET['click_id'];
$return['source_id']		= $_GET['t1'] . '_' . $_GET['t2'];
$return['is_sellable']		= $_GET['is_sellable'];

$return['email']			= $_GET['email'];
$return['username']			= $_GET['username'];
$return['password']			= $_GET['password'];
$return['gender']			= $_GET['gender'];
$return['looking']			= $_GET['looking'];
$return['dob']				= $_GET['dob'];

$return['city']				= $_GET['device']['geo']['city'];
$return['lander']			= $_GET['lander'];
$return['device']			= $_GET['device']['device'];
$return['http']				= $_GET['http'];
$return['index_id']			= $_GET['index_id'];
$return['antifraud']		= isset($_GET['antifraud']) ? json_encode($_GET['antifraud']) : '';


// Send Data to Log API
$log	= new Log($return);
$send	= $log->send($config->log->endpoint);

echo json_encode($return);