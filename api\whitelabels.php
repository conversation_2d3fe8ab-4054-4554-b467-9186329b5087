<?php
include_once('./log.php');

$config = file_exists('./config/api.json') ? json_decode(file_get_contents('./config/api.json')) : null;

/**
 * Make sure the configuration is not missing or empty
 */
if($config == null){
	$fileMissing = json_encode([
		'code' => 401,
		'message' => 'Api configuration file missing',
	]);
	die($fileMissing);
}

$offer = $_GET['offer'];

$startTime	= microtime(true);

$data = [
		"ip"				=> $_SERVER['REMOTE_ADDR'],
		"email"				=> $_GET['email'],
		"gender"			=> $_GET['gender'],
		"searchGender"		=> $_GET['seek'],
		"birthdate"			=> $_GET['dob'],
		"username"			=> $_GET['username'],
		'password'			=> $_GET['password'],
		'searchAgeFrom'		=> '22',
		'searchAgeTo'		=> '42',
];

// Do not include Email in data storage
$data['email'] = '************';

$return = [
	'code' => 200,
	'message' => 'Whitelabels',
	'url' => 'https://trakle01.online/8a8176dd-47ab-4891-8a41-00459e1db07d',
	'time' => '0',
	'request_data' => $data,
	'response_data' => '',
	'source' => 'whitelabels ' . $offer,
	'offer' => $offer
];

$endTime = microtime(true);
$return['key']				= $_GET['key'];
$return['response_data']	= 'Not Applicable - Whitelabels';
$return['start_time']		= $startTime;
$return['end_time']			= $endTime;
$return['endpoint']			= $config->whitelabels->endpoint->$offer;
$return['time'] 			= number_format( ( $endTime - $startTime ), 2);
$return['click_id']			= $_GET['click_id'];
$return['source_id']		= $_GET['source_id'];
$return['is_sellable']		= $_GET['is_sellable'];

$return['email']			= $_GET['email'];
$return['username']			= $_GET['username'];
$return['password']			= $_GET['password'];
$return['gender']			= $_GET['gender'];
$return['looking']			= $_GET['looking'];
$return['dob']				= $_GET['dob'];

$return['lander']			= $_GET['lander'];
$return['city']				= $_GET['city'];
$return['device']			= $_GET['device'];
$return['http']				= $_GET['http'];
$return['index_id']			= $_GET['index_id'];
$return['antifraud']		= isset($_GET['antifraud']) ? json_encode($_GET['antifraud']) : '';


// Send Data to Log API
$log	= new Log($return);
$send	= $log->send($config->log->endpoint);

echo json_encode($return);