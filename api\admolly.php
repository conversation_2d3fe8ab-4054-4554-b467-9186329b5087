<?php
include_once('./log.php');

$config = file_exists('./config/api.json') ? json_decode(file_get_contents('./config/api.json')) : null;

/**
 * Make sure the configuration is not missing or empty
 */
if($config == null){
	$fileMissing = json_encode([
		'code' => 401,
		'message' => 'Api configuration file missing',
	]);
	die($fileMissing);
}

$startTime	= microtime(true);
$offer		= isset($_GET['offer']) ? $_GET['offer'] : 'us';
$location	= array_rand($config->admolly->$offer->states, 1);
$country	= [
	"au" 	=> "Australia-",
	"ca"	=> "Canada-",
	"us"	=> "United States-"
];

$data = [
	'username' => $_GET['username'],
	'password' => $_GET['password'],
	'email' => $_GET['email'],
	'gender'	=> $_GET['gender'],
	'seek'		=> $_GET['looking'],
	'location'	=> $country[$offer].$config->admolly->$offer->states[$location],
	'birth_day'	=> $_GET['birth_day'],
    'birth_month'	=> $_GET['birth_month'],
	'birth_year'	=> $_GET['birth_year'],
	'ip'	=> $_SERVER['REMOTE_ADDR'],
	'device'	=> $_GET['media'],
	"req_id"	=> $config->admolly->$offer->req_id,
	"aff_id"	=> $config->admolly->$offer->aff_id . $_GET['aff_id'],
	"sub_id"	=> $_GET['sub_id'] // Generated Click ID
];

$ch = curl_init($config->admolly->$offer->endpoint);
curl_setopt_array($ch, array(
	CURLOPT_POST	=> true,
	CURLOPT_RETURNTRANSFER => true,
	CURLOPT_FOLLOWLOCATION => true,
	CURLOPT_HTTPHEADER => array(
		'Content-Type: application/json'
	),
	CURLOPT_POSTFIELDS => json_encode($data)
));

$response = curl_exec($ch);
$responseData = json_decode($response, true);
$httpcode = curl_getinfo($ch, CURLINFO_RESPONSE_CODE);
curl_close($ch);

// Do not include Email in data storage
$data['email'] = '************';

$return = [
	'code' => 200,
	'message' => '',
	'url' => '',
	'time' => '0',
	'request_data' => $data,
	'response_data' => '',
	'source' => 'Admolly '.$offer,
	'offer' => $offer
];

/**
 * 1. Check if user created successfully - build success response
 * 2. If user creation failed - build error response
 */
if($response != false){
	if(is_array($responseData) && array_key_exists('errors', $responseData)){
		$return['message'] = $responseData['errors']['email'][0];
		$return['code'] = $httpcode;
	}else{
		$jsonResponse = json_decode($response);
		if($httpcode != 200){
			$return['message'] = $jsonResponse->result;
			$return['code'] = $httpcode;
		}else{
			$return['message'] = 'Account successfully created';
			$return['url'] = $response;
		}
	}
}else{
	$responseData = 'Unable to connect API';
}

$endTime = microtime(true);
$return['key']				= $_GET['key'];
$return['response_data']	= $responseData ? $responseData : $response;
$return['start_time']		= $startTime;
$return['end_time']			= $endTime;
$return['endpoint']			= $config->admolly->$offer->endpoint;
$return['time'] 			= number_format( ( $endTime - $startTime ), 2);
$return['click_id']			= $_GET['sub_id'];
$return['source_id']		= $_GET['aff_id'];
$return['is_sellable']		= $_GET['is_sellable'];

$return['email']			= $_GET['email'];
$return['username']			= $_GET['username'];
$return['password']			= $_GET['password'];
$return['gender']			= $_GET['gender'];
$return['looking']			= $_GET['looking'];
$return['dob']				= $_GET['birth_year'].'-'.$_GET['birth_month'].'-'.$_GET['birth_day'];

$return['city']				= $_GET['city'];
$return['lander']			= $_GET['lander'];
$return['device']			= $_GET['device'];
$return['http']				= $_GET['http'];
$return['index_id']			= $_GET['index_id'];
$return['antifraud']		= isset($_GET['antifraud']) ? json_encode($_GET['antifraud']) : '';


// Send Data to Log API
$log	= new Log($return);
$send	= $log->send($config->log->endpoint);

echo json_encode($return);