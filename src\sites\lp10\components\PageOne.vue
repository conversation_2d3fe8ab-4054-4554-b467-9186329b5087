<script setup>
import { ref, defineProps, inject } from "vue";
import Steps from "../../lp9/components/views/StepsMarker.vue";
import Language from "../../../assets/js/helper/Language.js";
import Searching from "../../../sites/lp12/components//views/Searching.vue";

const props = defineProps(["steps", "moveNextSlide", "moveBackSlide", "language", "location"]);
const steps = ref(props.steps);
const assets = inject("assets");

const selectPreference = (preference) => {
  props.moveNextSlide(preference);
};
</script>

<template>
  <div class="container my-5">
    <div class="card p-4">
      <div class="header-logo">
        <img src="../../../assets/flirty.png" class="logofli" alt="" />
      </div>
      <div class="d-flex justify-content-center">
        <Searching :location="location" :language="Language" />
      </div>
      <form>
        <div class="form-group">
          <label for="gender" class="w-100 text-center fs-5">{{ Language.local_area }}</label>
          <hr />
          <div class="d-flex justify-content-around my-3">
            <div>
              <button type="button" class="btn btn-outline" for="option1" @click="selectPreference('no')">{{ Language.no }}</button>
            </div>
            <div>
              <button type="button" class="btn btn-outline" for="option2" @click="selectPreference('yes')">{{ Language.yes_i_am }}</button>
            </div>
          </div>
        </div>
      </form>
      <Steps :step="steps" />
      <div class="disclaimer">
        <div class="progress mb-3" role="progressbar" aria-label="Info example" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100">
          <div class="progress-bar bg-info" style="width: 14%"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<style>
body {
  background-color: #f0f8ff;
}
.card {
  max-width: 400px;
  margin: auto;
  padding: 20px;
  border-radius: 15px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.4);
  background-color: rgba(255, 255, 255, 0.7);
}

.header-logo {
  text-align: center;
  margin-bottom: 20px;
}
.header-logo img {
  width: 150px;
}
.header-text {
  text-align: center;
  font-size: 1.5rem;
  margin-bottom: 30px;
}
.form-group {
  margin-bottom: 15px;
}
.btn-primary {
  background-color: #6a1b9a;
  border-color: #6a1b9a;
  width: 100%;
  font-size: 1.2rem;
}
.disclaimer {
  text-align: center;
  margin-top: 20px;
}
.disclaimer small {
  color: #6c757d;
}

.btn-outline {
  width: 10em !important;
  height: 4em !important;
  border-color: #6a1b9a;
  color: black;
}

.btn-outline:hover {
  background-color: #6a1b9a;
  color: white;
}

.card {
  height: 25em !important;
  width: 25em;
}
.logofli {
  width: auto;
  height: 2em;
}
</style>
