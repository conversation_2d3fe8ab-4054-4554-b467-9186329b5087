<?php
include_once('./log.php');

$config = file_exists('./config/api.json') ? json_decode(file_get_contents('./config/api.json')) : null;

$startTime = microtime(true);

/**
 * Make sure the configuration is not missing or empty
 */
if($config == null){
	$fileMissing = json_encode([
		'code' => 401,
		'message' => 'Api configuration file missing',
	]);
	die($fileMissing);
}

$data = [
	'username' => $_GET['username'],
	'password' => $_GET['password'],
	'email' => $_GET['email'],
	'sex'	=> $_GET['sex'],
	'birthdate' => $_GET['birthdate'],
	'ip'	=> $_SERVER['REMOTE_ADDR'],
	'offerit_code' => $config->cummission->predefined_form->offerit_code,
	'require_valid_username' => $config->cummission->predefined_form->require_valid_username,
	'offerit_conversion_variables' => [
        '_ocid' => $_GET['clickid'],
        'subaff' => $_GET['sourceid'],
	]
];

$ch = curl_init($config->cummission->endpoint);
curl_setopt_array($ch, array(
	//CURLOPT_HEADER  => true,
	CURLOPT_POST	=> true,
	CURLOPT_RETURNTRANSFER => true,
	CURLOPT_FOLLOWLOCATION => true,
	CURLOPT_HTTPHEADER => array(
		'X-Auth-Token: ' .$config->cummission->token,
		'Content-Type: application/json'
	),
	CURLOPT_POSTFIELDS => json_encode($data)
));

$response = curl_exec($ch);
$responseData = json_decode($response, true);
$httpcode = curl_getinfo($ch, CURLINFO_RESPONSE_CODE);
curl_close($ch);


// Do not include Email in data storage
$data['email'] = '************';

$return = [
	'code' => 200,
	'message' => '',
	'url' => '',
	'time' => '0',
	'request_data' => $data,
	'response_data' => '',
	'source' => 'Cummission'
];

/**
 * 1. Check if user created successfully - build success response
 * 2. If user creation failed - build error response
 */
if($response != false){
	if(is_array($responseData) && array_key_exists('user_id', $responseData)){
		$return['message'] = 'Account successfully created';
		$return['url'] = $responseData['login_url'];
	}else{
		$return['message'] = $responseData['message'] . ' : ' . $responseData['errors'][0]['field'];
		$return['code'] = $httpcode;
	}
}else{
	$responseData = 'Unable to connect API';
}

$endTime = microtime(true);
$return['key']				= $_GET['key'];
$return['response_data']	= $responseData;
$return['start_time']		= $startTime;
$return['end_time']			= $endTime;
$return['endpoint']			= $config->cummission->endpoint;
$return['time']				= number_format( ( $endTime - $startTime ), 2);
$return['click_id']			= $_GET['clickid'];
$return['source_id']		= $_GET['sourceid'];
$return['is_sellable']		= $_GET['is_sellable'];

$return['email']			= $_GET['email'];
$return['username']			= $_GET['username'];
$return['password']			= $_GET['password'];
$return['gender']			= $_GET['sex'];
$return['looking']			= $_GET['looking'];
$return['dob']				= $_GET['birthdate'];

$return['device']			= $_GET['device'];
$return['city']				= $_GET['city'];
$return['lander']			= $_GET['lander'];
$return['http']				= $_GET['http'];
$return['index_id']			= $_GET['index_id'];
$return['antifraud']		= isset($_GET['antifraud']) ? json_encode($_GET['antifraud']) : '';


// Send Data to Log API
$log	= new Log($return);
$send	= $log->send($config->log->endpoint);

echo json_encode($return);