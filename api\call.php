<?php


class Call {

    private $endpoint;
    private $httpHeader;
    private $body;
    private $init;
    public $info;
    private $method;
    private $response;

    public function __construct($endpoint = false, $header = false, $body = false, $method = false){
        $this->endpoint     = $endpoint;
        $this->httpHeader   = $header;
        $this->body         = $body;
        $this->method       = $method;
        $this->init();
    }

    private function init(){
        $this->init = curl_init($this->endpoint);
        $this->options();
        $this->response = $this->execute();
        $this->info = $this->info();
        $this->close();
    }

    private function options(){
        $options = curl_setopt_array($this->init, array(
            CURLOPT_CUSTOMREQUEST   => $this->method ? 'POST' : 'GET',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTPHEADER => $this->httpHeader,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_POSTFIELDS => json_encode($this->body)
        ));
        return $options;
    }

    private function execute(){
        return curl_exec($this->init);
    }

    private function info(){        
        return curl_getinfo($this->init, CURLINFO_RESPONSE_CODE);
    }

    private function close(){
        curl_close($this->init);
    }

    public function responseJSON(){
        return $this->response;
    }

    public function responseObject(){
        return json_decode($this->response);
    }

    public function responseArray(){
        return json_decode($this->response, true);
    }


}