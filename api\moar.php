<?php
include_once('./log.php');

$config = file_exists('./config/api.json') ? json_decode(file_get_contents('./config/api.json')) : null;

$startTime = microtime(true);

/**
 * Make sure the configuration is not missing or empty
 */
if($config == null){
	$fileMissing = json_encode([
		'code' => 401,
		'message' => 'Api configuration file missing',
	]);
	die($fileMissing);
}

$offer = $_GET['offer'];


// Replace URL placeholder
// $placeholders			= ['{affid}', '{clickid}', '{subid}', '{subid2}'];
// $placeholdersReplace	= [ $config->moar->$offer->affid, $_GET['click_id'], $_GET['subid'], $_GET['subid2']];
// $endpoint				= str_replace($placeholders, $placeholdersReplace, $config->moar->$offer->endpoint);

$endpoint = $config->moar->$offer->url;

$md5Hash = $_GET['traf_id'] ? md5($_GET['traf_id']) : "";
$traf_id = $md5Hash ? substr($md5Hash, 0, 16) : ""; 

$traf_id = $traf_id ? $traf_id . "_" : "";
$sub = $traf_id . $_GET['t1'];

if(str_contains($offer, 'wellhello')){
	$data = [
		'email' 	=> $_GET['email'],
		'password'	=> $_GET['gender'],
		'birth_date'=> $_GET['dob'],
		'client_ip' => $_GET['device']['ip'],
		'tracking'	=> [
			'clickid' => $_GET['click_id'],
		],
		'gender' 	=> $_GET['gender'],
		'country'	=> $_GET['device']['geo']['country'],
		'zip_code'	=> $_GET['device']['geo']['postal_code'],
		'sub_id'	=> $sub,
	];
}
else{

	$dob = $_GET['dob'];
	$dobDate = new DateTime($dob);
	$currentDate = new DateTime();
	$age = $dobDate->diff($currentDate)->y;

	$data = [
		'email' 	=> $_GET['email'],
		'password'	=> $_GET['gender'],
		'age'		=> $age,
		'client_ip' => $_GET['device']['ip'],
		'tracking'	=> [
			'click_id' => $_GET['click_id'],
		],
		'gender' 	=> $_GET['gender'],
		'country'	=> $_GET['device']['geo']['country_code'],
		'username'	=> $_GET['username'],
		'affiliate_subid' => $sub,
	];
}


$ch = curl_init($endpoint);
curl_setopt_array($ch, array(
	CURLOPT_POST	=> true,
	CURLOPT_RETURNTRANSFER => true,
	CURLOPT_FOLLOWLOCATION => true,
	CURLOPT_SSL_VERIFYHOST => false,
    CURLOPT_SSL_VERIFYPEER => false,
	CURLOPT_HTTPHEADER => array(
		'Authorization: '.$config->moar->$offer->token,
		'Content-Type: application/json'
	),
	CURLOPT_POSTFIELDS => json_encode($data)
));

$response = curl_exec($ch);
$responseData = json_decode($response, true);

$httpcode = curl_getinfo($ch, CURLINFO_RESPONSE_CODE);

curl_close($ch);

// Do not include Email in data storage
$data['email'] = '************';

$return = [
	'code' => 200,
	'message' => '',
	'url' => '',
	'time' => '0',
	'request_data' => $data,
	'response_data' => '',
	'source' => 'Moar '.$offer
];

/**
 * 1. Check if user created successfully - build success response
 * 2. If user creation failed - build error response
 */
if($response != false){
	if(str_contains($offer, 'wellhello')){
		if(is_array($responseData)){
			if($httpcode == 200 || $httpcode == 201){
				$return['message'] = "Account successfully created";
				$return['code'] = 200;
				$return['url']	= $responseData['data']['auto_login'];
			}else{
				$return['message'] = json_encode($responseData['data']['error']);
				$return['code'] = $responseData['status_code'];
			}
		}
	}
	else{
		if(is_array($responseData)){
			if($httpcode == 200 || $httpcode == 201){
				$return['message'] = "Account successfully created";
				$return['code'] = 200;
				$return['url']	= $responseData['data']['auto_login'];
			}else{
				$error_msg = '';
				foreach($responseData['errors'] as $k => $v){
					$error_msg = $v;
				}

				$return['message'] = $error_msg;
				$return['code'] = 401;
			}
		}
	}

}else{
	$responseData = 'Unable to connect API';
}



$endTime = microtime(true);
$return['key']				= $_GET['key'];
$return['response_data']	= $responseData;
$return['start_time']		= $startTime;
$return['end_time']			= $endTime;
$return['endpoint']			= $endpoint;
$return['time']				= number_format( ( $endTime - $startTime ), 2);
$return['click_id']			= $_GET['click_id'];
$return['source_id']		= $_GET['subid'];
$return['is_sellable']		= $_GET['is_sellable'];

$return['email']			= $_GET['email'];
$return['username']			= $_GET['username'];
$return['password']			= $_GET['password'];
$return['gender']			= $_GET['gender'];
$return['looking']			= $_GET['looking'];
$return['dob']				= $_GET['dob'];

$return['device']			= $_GET['device'];
$return['city']				= $_GET['city'];
$return['lander']			= $_GET['lander'];
$return['http']				= $_GET['http'];
$return['index_id']			= $_GET['index_id'];
$return['antifraud']		= isset($_GET['antifraud']) ? json_encode($_GET['antifraud']) : '';


// Send Data to Log API
$log	= new Log($return);
$send	= $log->send($config->log->endpoint);

echo json_encode($return);