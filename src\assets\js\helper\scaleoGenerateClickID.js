import axios from 'axios'
import endpoints from '../config/endpoints.json'
import params from './urlParameters.js'

const scaleoClickID = (formData) => {

    if(params().click_id && params().click_id.length > 0){
        return params().click_id
    }else{
        const httpRequest = async (formData) => {
        // Generate click id first
            const clickIdParams = {
                'affiliate_id': params().affiliate ? params().affiliate : 3,
                'offer_id': params().offer ? params().offer : 2
            }
            return await axios.get(endpoints.click_id, { params: clickIdParams})
        }
        return httpRequest()
    }

}

export default scaleoClickID