<?php
include_once('./log.php');

$config		= file_exists('./config/api.json') ? json_decode(file_get_contents('./config/api.json')) : null;

$startTime	= microtime(true);
$offer		= $_GET['offer'];

/**
 * Make sure the configuration is not missing or empty
 */
if($config == null){
	$fileMissing = json_encode([
		'code' => 401,
		'message' => 'Api configuration file missing',
	]);
	die($fileMissing);
}

$data = [
	/**
	 * Mandatory parameters
	 */
	'apiKey'				=> $config->together->$offer->api_key,
	'email' 				=> $_GET['email'],
	'sexual_orientation'	=> $_GET['sexual_orientation'],
	'gender'				=> $_GET['gender'],
	'dob'					=> $_GET['dob'],
	'ip'					=> $_SERVER['REMOTE_ADDR'],
	'ua' 					=> $_GET['ua'],
	'utm_medium' 			=> $_GET['utm_medium'],
	"tdsId"					=> $config->together->$offer->predefined->tdsId,
	"utm_campaign"			=> $config->together->$offer->predefined->utm_campaign,
	"utm_source"			=> $config->together->$offer->predefined->utm_source,
	"affid"					=> $config->together->$offer->predefined->affid,
	"tds_campaign"			=> $config->together->$offer->predefined->tds_campaign,
	"s1"					=> $config->together->$offer->predefined->s1,

	/**
	 * Optional parameters
	 */
	'data2' => $_GET['click_id'],
	'utm_content' => $_GET['t1'],
	's3' => $_GET['t2']
];
 

// https://find-me-sex.com/tdsApi?tdsId=s7595and_r&tds_campaign=s7595and&utm_source=ddm&utm_campaign=fe23b52d&utm_content={subid}&data2={data2}&s1=dd
$endpoint = $config->together->$offer->endpoint;
$endpoint = str_replace(array('{sub1}', '{sub2}', '{data2}'), array($_GET['t1'], $_GET['t2'] , $_GET['click_id']), $endpoint);

$ch = curl_init($endpoint);
curl_setopt_array($ch, array(
	CURLOPT_POST	=> true,
	CURLOPT_RETURNTRANSFER => true,
	CURLOPT_FOLLOWLOCATION => true,
	CURLOPT_HTTPHEADER => array(
		'Content-Type: application/json'
	),
	CURLOPT_POSTFIELDS => json_encode($data)
));

$response = curl_exec($ch);
$responseData = json_decode($response, true);
$httpcode = curl_getinfo($ch, CURLINFO_RESPONSE_CODE);
curl_close($ch);

// Do not include Email in data storage
$data['email'] = '************';

$return = [
	'code' => 200,
	'message' => '',
	'url' => '',
	'time' => '0',
	'request_data' => $data,
	'response_data' => '',
	'source' => 'Together '. $offer
];

/**
 * 1. Check if user created successfully - build success response
 * 2. If user creation failed - build error response
 */
if($response != false){
	if(is_array($responseData)){
		if($responseData['status'] != 'success'){
			$return['message'] = $responseData['message'];
			$return['code'] = $responseData['code'];
		}else{
			$return['message'] = $responseData['status'];
			$return['code'] = $httpcode;
			$return['url']	= $responseData['location'];
		}
	}
}else {
	$responseData = 'Unable to connect API';
}

$endTime = microtime(true);
$return['key']				= $_GET['key'];
$return['response_data']	= $responseData;
$return['start_time']		= $startTime;
$return['end_time']			= $endTime;
$return['endpoint']			= $endpoint;
$return['time'] 			= number_format( ( $endTime - $startTime ), 2);
$return['click_id']			= $_GET['click_id'];
$return['source_id']		= $_GET['source_id'];
$return['is_sellable']		= $_GET['is_sellable'];

$return['email']			= $_GET['email'];
$return['username']			= $_GET['username'];
$return['password']			= $_GET['password'];
$return['gender']			= $_GET['gender'];
$return['looking']			= $_GET['looking'];
$return['dob']				= $_GET['dob'];

$return['device']			= $_GET['device'];
$return['city']				= $_GET['city'];
$return['lander']			= $_GET['lander'];
$return['http']				= $_GET['http'];
$return['index_id']			= $_GET['index_id'];
$return['antifraud']		= isset($_GET['antifraud']) ? json_encode($_GET['antifraud']) : '';


// Send Data to Log API
$log	= new Log($return);
$send	= $log->send($config->log->endpoint);

echo json_encode($return);