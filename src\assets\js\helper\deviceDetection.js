

const device = () => {

    const ua = navigator.userAgent;

        const browser = () => {
            if(ua.match(/chrome|chromium|crios/i)){
                return "Chrome"
            }else if(ua.match(/firefox|fxios/i)){
                return "Firefox"
            }else if(ua.match(/safari/i)){
                return "Safari"
            }else if(ua.match(/opr\//i)){
                return "Opera"
            } else if(ua.match(/edg/i)){
                return "Edge (Microsoft)"
            }else{
                return 'Unknown'
            }
        }

        // Device Detection
        const device = () => {
            if (/(tablet|ipad|playbook|silk)|(android(?!.*mobi))/i.test(ua)) {
                return "Tablet";
            }
            else if (/Mobile|Android|iP(hone|od)|IEMobile|BlackBerry|Kindle|Silk-Accelerated|(hpw|web)OS|Opera M(obi|ini)/.test(ua)) {
                return "Mobile";
            }
            return "Desktop";
        }

    return {
        'browser': browser(),
        'device': device(),
        'ua_raw': ua
    }

}

export default device