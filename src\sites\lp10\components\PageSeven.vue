<script setup>
import { ref, defineProps, onMounted, inject } from "vue";
import Steps from "../../lp9/components/views/StepsMarker.vue";
import Language from "../../../assets/js/helper/Language.js";
import Api from "../../../assets/js/helper/api.js";
import Swal from "sweetalert2";
import Searching from "../../lp14/components/views/Searching.vue";

const props = defineProps(["inputs", "steps", "moveNextSlide"]);
const steps = ref(props.steps);
const apiErrorMessage = ref(false);
const disableSubmitBtn = ref(false);
const assets = inject("assets");
const showSearching = ref(false);

const validationMessages = ref({
  username: false,
  dob: false,
  email: false,
  password: false,
  privacy: false,
  privacy_service: false,
});

onMounted(() => {
  window.dataLayer?.push({
    event: "page_view",
  });

  const uncheckedCountries = ["AT", "BE", "BG", "HR", "CY", "CZ", "DK", "EE", "FI", "FR", "DE", "GR", "HU", "IE", "IT", "LV", "LT", "LU", "MT", "NL", "PL", "PT", "RO", "SK", "SI", "ES", "SE", "AD", "BA", "IS", "LI", "MC", "MD", "ME", "MK", "NO", "RS", "CH", "UA", "GB", "VA", "SM"];

  if (props.inputs.device.value?.geo?.country_code && !uncheckedCountries.includes(props.inputs.device.value.geo.country_code)) {
    props.inputs.privacy.value = true;

    const privacyCheckbox = document.getElementById("privacyCheck");
    if (privacyCheckbox) {
      privacyCheckbox.checked = true;
    }
  }
});

const validateForm = async (event) => {
  // Push event to trigger GTM tag
  window.dataLayer?.push({
    event: "button_click",
    Click_Classes: "button_click", // Matches trigger condition "Click Classes contains button_click"
    _event: "gtm.linkClick", // Matches trigger condition "_event equals gtm.linkClick"
    _triggers: "203887914_4", // Matches trigger condition "_triggers matches RegEx"
    "gtm.element": event.target,
    "gtm.elementClasses": "button_click g-recaptcha btn border btn-outline-12 rounded-5 px-5",
    "gtm.elementId": "submit-button",
  });

  // Log for debugging
  console.log("GTM Event Pushed:", {
    event: "button_click",
    Click_Classes: "button_click",
    _event: "gtm.linkClick",
    _triggers: "203887914_4",
  });

  if (!validateUsername()) {
    return false;
  }
  if (!validateDOB()) {
    return false;
  }
  if (!validateEmail()) {
    return false;
  }
  if (!validatePassword()) {
    const alertMessage = validationMessages.value.password;
    Swal.fire({
      text: alertMessage,
      customClass: {
        popup: "custom-swal-popup",
      },
    });
    return false;
  }
  if (!validatePrivacy()) {
    const alertMessage = validationMessages.value.privacy;
    Swal.fire({
      text: alertMessage,
      customClass: {
        popup: "custom-swal-popup",
      },
    });
    return false;
  }

  // Show searching screen
  showSearching.value = true;

  // Prepare form data
  let formData = {};
  for (let x in props.inputs) {
    formData[x] = props.inputs[x]["value"];
  }

  // Wait for 3 seconds before making API call
  await new Promise((resolve) => setTimeout(resolve, 1000));

  // Make API call
  // Api(formData, disableSubmitBtn, apiErrorMessage);

  return true;
  e;
};

const validatePrivacy = () => {
  if (props.inputs.privacy.value == false) {
    validationMessages.value.privacy = Language.alert_update;
    return false;
  } else {
    validationMessages.value.privacy = false;
    return true;
  }
};

// const validatePrivacyService = () => {
//   if (props.inputs.privacy_service.value == false) {
//     validationMessages.value.privacy_service = Language.alert_update;
//     return false;
//   } else {
//     validationMessages.value.privacy_service = false;
//     return true;
//   }
// };

const validatePassword = () => {
  let x = props.inputs.password.value;
  if (x.length > 14 || x.length < 6 || validator(x) == false || explicitValidator(x)) {
    validationMessages.value.password = Language.password_error_2;
    return false;
  } else {
    validationMessages.value.password = false;
    return true;
  }
};

const urlParams = new URLSearchParams(window.location.search);
const isUsernameZero = urlParams.get("username") === "all";

const validateUsername = () => {
  let x = props.inputs.username.value;
  const usernameRegex = /^(?=.*[0-9])(?=.*[a-zA-Z]).{6,14}$/;
  const usernameSimpleRegex = /^.{6,14}$/;

  if (isUsernameZero) {
    if (!usernameSimpleRegex.test(x)) {
      validationMessages.value.username = Language.username_error_3;
      return false;
    } else {
      validationMessages.value.username = false;
      return true;
    }
  } else {
    if (x.length > 14 || x.length < 6 || validator(x) == false || explicitValidator(x)) {
      validationMessages.value.username = Language.username_error_2;
      return false;
    } else {
      validationMessages.value.username = false;
      return true;
    }
  }
};

const validateEmail = () => {
  if (/^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,})+$/.test(props.inputs.email.value)) {
    validationMessages.value.email = false;
    return true;
  } else {
    validationMessages.value.email = Language.email_error;
    return false;
  }
};

const validator = (str) => {
  return /[0-9][a-zA-Z]|[a-zA-Z][0-9]/.test(str);
};

const explicitValidator = (str) => {
  const format = /[ `!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~]/;
  return format.test(str);
};

const validateDOB = () => {
  if (props.inputs.birth_day.value == "" || props.inputs.birth_year.value == "" || props.inputs.birth_month.value == "") {
    validationMessages.value.dob = Language.select_your_age;
    return false;
  } else {
    validationMessages.value.dob = false;
    return true;
  }
};
</script>

<template>
  <Searching v-if="showSearching" />

  <div class="container my-5">
    <div class="cardS p-4">
      <div class="header-logo">
        <img src="../../../assets/flirty.png" class="logofli" alt="" />
      </div>
      <form id="demo-form">
        <div class="form-group">
          <label for="gender" class="w-100 text-center fs-5">{{ Language.password_create }}</label>
          <hr />
          <div class="d-flex justify-content-around my-2">
            <div class="d-flex justify-content-center flex-column gap-3">
              <input v-model="inputs.password.value" class="form-control rounded-5 px-5" placeholder="Enter Your Password" type="password" @focus="onFocus" @blur="onBlur" @keypress="handleKeyPress" />
              <div class="others">
                <div class="d-flex flex-column align-items-center justify-content-center">
                  <div class="notice">
                    <p>
                      <strong> {{ Language.basic_info }}</strong> <br />
                      <strong>{{ Language.data_controller }}</strong> {{ Language.leads }} <a class="cursor-pointer text-danger" href="/privacy">{{ Language.info }}</a> <br />
                      <strong>{{ Language.purpose }}</strong> {{ Language.data_response }} <a class="cursor-pointer text-danger" href="/privacy">{{ Language.info }}</a> <br />
                      <strong>{{ Language.rights }}</strong
                      >{{ Language.data_access }} <a class="cursor-pointer text-danger" href="/privacy">{{ Language.info }}</a> <br />
                      <strong>{{ Language.additional_info }}</strong
                      >{{ Language.data_protect }} <a class="cursor-pointer text-danger" href="/privacy">{{ Language.privacy_policy }}</a>
                    </p>
                  </div>
                  <div class="accept_agreed">
                    <div class="d-flex align-items-start mb-1">
                      <input type="checkbox" id="privacyCheck" v-model="inputs.privacy.value" class="custom-checkbox mt-1" />
                      <label for="privacyCheck" class="agree text-dark ms-2">{{ Language.privacy_agree }}</label>
                    </div>
                    <!-- <div class="d-flex align-items-start mb-1">
                      <input type="checkbox" id="serviceCheck" v-model="inputs.privacy_service.value" class="custom-checkbox mt-1" />
                      <label for="serviceCheck" class="agree text-dark ms-2">{{ Language.privacy_service_agree }}</label>
                    </div> -->
                    <!-- <div class="d-flex align-items-start mb-1">
                      <input type="checkbox" id="promotionCheck" v-model="inputs.promotion_agree.value" class="custom-checkbox mt-1" />
                      <label for="promotionCheck" class="agree text-dark ms-2">{{ Language.privacy_promotion_agree }}</label>
                    </div> -->
                  </div>
                  <div
                    v-if="
                      [
                        'Los Angeles',
                        'San Diego',
                        'Santa Monica',
                        'San Francisco',
                        'San Jose',
                        'Fresno',
                        'Sacramento',
                        'Long Beach',
                        'Oakland',
                        'Bakersfield',
                        'Anaheim',
                        'Santa Ana',
                        'Riverside',
                        'Stockton',
                        'Chula Vista',
                        'Irvine',
                        'Fremont',
                        'San Bernardino',
                        'Modesto',
                        'Oxnard',
                        'Fontana',
                      ].includes(inputs.location.value)
                    "
                    class="text-center mt-3"
                  >
                    <input type="checkbox" id="flexCheckDefault" v-model="inputs.is_sellable.value" />
                    <label class="fs-6 ms-2" for="flexCheckDefault">{{ Language.data_permission }}</label>
                  </div>
                </div>
              </div>
              <button type="button" class="g-recaptcha btn border btn-outline-12 rounded-5 px-5 final-submit-btn" @click="(event) => validateForm(event)" :class="`${disableSubmitBtn ? 'disabled' : ''}`">
                {{ Language.btn_submit }}
                <div class="spinner-border spinner-grow-sm text-light" v-show="disableSubmitBtn" role="status">
                  <span class="visually-hidden">{{ Language.loading }}</span>
                </div>
              </button>
              <!-- <button type="button" class="g-recaptcha btn border btn-outline-12 rounded-5 px-5" role="button" data-sitekey="6LcVHV4qAAAAAOz2INgLc2XPKtP5OdnJTktATdPS" data-callback="onSubmit" data-action="submit" :class="`${disableSubmitBtn ? 'disabled' : ''}`">
                {{ Language.btn_submit }}
                <div class="spinner-border spinner-grow-sm text-light" v-show="disableSubmitBtn" role="status">
                  <span class="visually-hidden">{{ Language.loading }}</span>
                </div>
              </button> -->
              <div class="text-center">
                <p class="text-dark" style="font-size: 10px">{{ Language.spam_alert }}</p>
              </div>
            </div>
          </div>
        </div>
      </form>
      <div id="alert-message3" style="display: none">
        {{ Language.alert_update }}
      </div>
      <div id="alert-message" style="display: none">
        {{ Language.alert_update }}
      </div>
      <div id="alert-message2" style="display: none">
        {{ Language.password_error_2 }}
      </div>
      <Steps :step="steps" />
      <div class="disclaimer mt-2">
        <div class="progress mb-3" role="progressbar" aria-label="Info example" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100">
          <div class="progress-bar bg-info" style="width: 100%"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<style>
body {
  background-color: #f0f8ff;
}
.notice {
  font-size: 7px !important;
}

.accept_agreed .form-check-input {
  width: 20px; /* Set a fixed width */
  height: 20px; /* Set a fixed height */
}

.accept_agreed {
  font-size: 13px !important;
}
.cardS {
  max-width: 400px;
  margin: auto;
  padding: 20px;
  border-radius: 15px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.4);
  background-color: rgba(255, 255, 255, 0.7);
  height: 36em !important;
  width: 25em;
}
.header-logo {
  text-align: center;
  margin-bottom: 20px;
}
.header-logo img {
  width: 150px;
}
.header-text {
  text-align: center;
  font-size: 1.5rem;
  margin-bottom: 30px;
}
.form-group {
  margin-bottom: 15px;
}
.btn-primary {
  background-color: #6a1b9a;
  border-color: #6a1b9a;
  font-size: 1.2rem;
}
.disclaimer {
  text-align: center;
  margin-top: 20px;
}
.disclaimer small {
  color: #6c757d;
}

.btn-outline-12 {
  border-color: #6a1b9a;
  color: black;
}

.btn-outline-12:hover {
  background-color: #6a1b9a;
  color: white;
}

.legal {
  font-size: 12px !important;
}
</style>
