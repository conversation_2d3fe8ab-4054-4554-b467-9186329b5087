<script setup>
import { ref, onMounted } from "vue";
import Params from "../../assets/js/helper/urlParameters.js";
import axios from "axios";

const url = Params().url.replace("?", "");
const click_id = Params().click_id;
const { step, next, inputData } = defineProps(["step", "next", "inputData", "language"]);
const l = ref(Params().l);

var underAgeLinks = {
  us: "https://trakle02.online/tracker/100",
  fr: "https://trakle02.online/tracker/100",
};

const underAgeSelector = () => {
  if (l.value == "fr") {
    let myUrl = underAgeLinks.us + Params().url;
    return myUrl + "&clickid=" + click_id;
  } else {
    let myUrl = underAgeLinks.us + Params().url;
    return myUrl;
  }
};

// Random DOB Generator
const dob = (min, max) => {
  let random = Math.floor(Math.random() * (parseInt(max) - parseInt(min))) + min;
  inputData.birth_year.value = new Date().getFullYear() - (random + 1);
  inputData.birth_month.value = (Math.floor(Math.random() * (parseInt(12) - parseInt(1))) + 1).toString();
  inputData.birth_day.value = (Math.floor(Math.random() * (parseInt(29) - parseInt(1))) + 1).toString();

  inputData.birth_day.value.length == 1 ? (inputData.birth_day.value = "0" + inputData.birth_day.value) : "";
  inputData.birth_month.value.length == 1 ? (inputData.birth_month.value = "0" + inputData.birth_month.value) : "";

  next();
};
</script>

<template>
  <div class="card text-center t-bg mx-auto" v-if="step == 1">
    <div class="card-body">
      <div class="mt-auto text-center slides rounded card-black p-2">
        <div class="mb-4">
          <h5 class="card-title text-pink fs-6 text-uppercase">{{ language.age_question }}</h5>
        </div>
        <a class="btn btn-sm fs-6 text-white next-btn mt-2 me-lg-2 me-md-2 col-8 col-lg-auto col-md-auto" :href="underAgeSelector()" type="button">18 - 24</a>
        <a class="btn btn-sm fs-6 text-white next-btn mt-2 me-lg-2 me-md-2 col-8 col-lg-auto col-md-auto" :href="underAgeSelector()" type="button">25 - 29</a>
        <button class="btn btn-sm fs-6 text-white next-btn mt-2 me-lg-2 me-md-2 col-8 col-lg-auto col-md-auto" @click="dob(30, 45)" type="button">30 - 45</button>
        <button class="btn btn-sm fs-6 text-white next-btn mt-2 me-lg-2 me-md-2 col-8 col-lg-auto col-md-auto" @click="dob(46, 99)" type="button">46 - 99</button>
      </div>
    </div>
  </div>
</template>

<style></style>
