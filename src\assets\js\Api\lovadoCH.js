import axios from 'axios'
import endpoints from '../../../assets/js/config/endpoints.json'
import params from '../helper/urlParameters.js'

const lovadoch = async (formData) => {

    const httpData = {
        
        username            :formData.username,
        password            :formData.password,
        email               :formData.email,
        gender              :formData.gender,
        looking             :formData.seek,
        location            :formData.location,
        birth_day           :formData.birth_day,
        birth_month         :formData.birth_month,
        birth_year          :formData.birth_year,
        click_id            :formData.click_id,
        req_id              :params().offer, // Unsure what is this, need to ask to team
        source_id           :formData.cmp, // Source ID
        key                 :formData.key, // Unique Identifier for Logger
        is_sellable         :!formData.is_sellable,
        lander              :formData.lander,
        device              :formData.device,
        http                :formData.http,
        offer               :"ch",
        city                :formData.location,
        index_id            : "43",
        t1                  : formData.t1,
        t2                  : formData.t2,
        traf_id             : formData.traf_id,
        antifraud   : formData.antifraud,
    }

    return await axios.get(endpoints.lovado, { params: httpData })
}

export default lovadoch